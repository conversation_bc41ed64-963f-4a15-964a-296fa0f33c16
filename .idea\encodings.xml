<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-biz-data-permission/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-biz-data-permission/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-biz-ip/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-biz-ip/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-biz-tenant/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-biz-tenant/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-excel/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-excel/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-job/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-job/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-monitor/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-monitor/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-mq/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-mq/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-mybatis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-mybatis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-protection/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-protection/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-redis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-redis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-security/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-security/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-test/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-test/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-web/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-web/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-websocket/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/mandong-spring-boot-starter-websocket/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-framework/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-ai/mandong-module-ai-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-ai/mandong-module-ai-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-ai/mandong-module-ai-biz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-ai/mandong-module-ai-biz/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-ai/mandong-spring-boot-starter-ai/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-ai/mandong-spring-boot-starter-ai/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-ai/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-ai/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-erp/mandong-module-erp-biz/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-erp/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-erp/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-infra/mandong-module-infra-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-infra/mandong-module-infra-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-infra/mandong-module-infra-biz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-infra/mandong-module-infra-biz/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-infra/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-infra/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-report/mandong-module-report-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-report/mandong-module-report-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-report/mandong-module-report-biz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-report/mandong-module-report-biz/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-report/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-report/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-system/mandong-module-system-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-system/mandong-module-system-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-system/mandong-module-system-biz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-system/mandong-module-system-biz/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-system/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-module-system/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/mandong-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
  </component>
</project>