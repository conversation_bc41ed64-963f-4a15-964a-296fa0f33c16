# Google Identity Services 登录配置指南

## 1. Google Identity Services 配置

### 1.1 在 Google Cloud Console 中创建项目

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Google Identity API

### 1.2 创建 OAuth 2.0 客户端 ID

1. 在 Google Cloud Console 中，导航到 "APIs & Services" > "Credentials"
2. 点击 "Create Credentials" > "OAuth client ID"
3. 选择应用程序类型为 "Web application"
4. 设置授权的 JavaScript 源：`http://localhost:3001`
5. 记录生成的客户端 ID

### 1.3 技术实现说明

本实现使用 Google Identity Services，直接验证 ID Token：

1. **前端获取ID Token**: 使用 Google Identity Services 获取 ID Token
2. **后端验证ID Token**: 直接验证和解析 Google 返回的 ID Token
3. **用户匹配**: 优先使用email，其次使用Google ID作为用户名查找系统用户

### 1.4 配置应用程序

在 `application.yaml` 中配置：

```yaml
google:
  oauth:
    client-id: "your-google-client-id"
    client-secret: "your-google-client-secret"  # 对于ID Token验证，可选
    redirect-uri: ""  # 对于Google Identity Services，不需要
    token-url: "https://www.googleapis.com/oauth2/v4/token"
    user-info-url: "https://www.googleapis.com/oauth2/v2/userinfo"
```

## 2. API 接口使用

### 2.1 Google 登录接口

**接口路径：** `POST /admin-api/erp/game/google-login`

**请求参数：**
```json
{
  "idToken": "string",     // 必填：Google ID Token (JWT)
  "googleId": "string",    // 可选：Google用户ID
  "email": "string",       // 可选：用户邮箱
  "name": "string",        // 可选：用户姓名
  "gameId": 123,          // 可选：游戏ID
  "productId": ["1", "2"] // 可选：产品ID数组，用于限制用户只能登录指定产品
}
```

**成功响应：**
```json
{
  "code": 0,
  "msg": "登录成功",
  "data": {
    "uid": 12345,
    "username": "用户名",
    "productName": "产品名称",
    "productId": 123,
    "channelName": "渠道名称",
    "isGuest": 0,
    "userStatus": 1,
    "regTime": *************,
    "totalAmount": 0.0,
    "loginTotal": 1,
    "deviceId": "设备ID",
    "callbackUrl": "回调URL",
    "callbackKey": "回调密钥",
    "productCode": "产品代码"
  }
}
```

**错误响应：**
```json
{
  "code": **********,
  "msg": "登录失败",
  "data": null
}
```

## 3. 前端集成示例

### 3.1 引入 Google Identity Services

```html
<!-- 在HTML页面中引入Google Identity Services -->
<script src="https://accounts.google.com/gsi/client" async defer></script>
```

### 3.2 初始化 Google 登录

```javascript
// 初始化Google Identity Services
function initializeGoogleSignIn() {
  google.accounts.id.initialize({
    client_id: 'your-google-client-id',
    callback: handleCredentialResponse
  });

  // 渲染登录按钮
  google.accounts.id.renderButton(
    document.getElementById('google-signin-button'),
    {
      theme: 'outline',
      size: 'large',
      text: 'signin_with',
      locale: 'zh_CN'
    }
  );

  // 可选：显示一键登录提示
  google.accounts.id.prompt();
}

// 处理Google登录回调
function handleCredentialResponse(response) {
  // response.credential 就是ID Token
  console.log('ID Token:', response.credential);

  // 调用后端登录接口
  loginWithGoogle(response.credential);
}
```

### 3.3 调用登录接口

```javascript
// 使用ID Token调用登录接口
async function loginWithGoogle(idToken) {
  try {
    const loginResponse = await fetch('/admin-api/erp/game/google-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        idToken: idToken,
        gameId: 123,        // 可选
        productId: ['1', '2'] // 可选
      })
    });

    const result = await loginResponse.json();
    if (result.code === 0) {
      // 登录成功，处理用户信息
      console.log('用户信息:', result.data);
      // 保存用户信息到本地存储或状态管理
      localStorage.setItem('userInfo', JSON.stringify(result.data));
      // 跳转到主页面
      window.location.href = '/dashboard';
    } else {
      // 登录失败
      console.error('登录失败:', result.msg);
      alert('登录失败: ' + result.msg);
    }
  } catch (error) {
    console.error('请求失败:', error);
    alert('网络错误，请稍后重试');
  }
}

// 页面加载完成后初始化
window.onload = function() {
  initializeGoogleSignIn();
};
```

### 3.4 完整的HTML示例

```html
<!DOCTYPE html>
<html>
<head>
    <title>Google 登录示例</title>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
</head>
<body>
    <h1>Google 登录</h1>
    <div id="google-signin-button"></div>

    <script>
        // 这里放入上面的JavaScript代码
    </script>
</body>
</html>
```

## 4. 用户匹配逻辑

系统使用以下逻辑查找用户：

1. **优先使用邮箱**: 如果Google用户有邮箱，首先使用邮箱作为用户名查找
2. **备用Google ID**: 如果邮箱查找失败，使用Google的openid作为用户名查找
3. **产品过滤**: 如果请求中指定了productId，只在指定产品中查找用户
4. **预注册要求**: 用户必须在系统中预先存在，Google登录不会自动创建新用户

## 5. 注意事项

1. **用户预注册**: 当前实现要求用户必须在系统中预先存在，Google 登录只是验证身份
2. **安全性**: 
   - 确保 HTTPS 在生产环境中启用
   - 定期轮换 Google OAuth 客户端密钥
   - 验证 state 参数以防止 CSRF 攻击
3. **错误处理**: 如果 Google 登录失败，接口会返回通用的登录失败错误，具体错误信息会记录在服务器日志中

## 6. 故障排除

### 6.1 常见错误

- **invalid_client**: 检查客户端 ID 和密钥是否正确
- **redirect_uri_mismatch**: 确保重定向 URI 与 Google Console 中配置的完全一致
- **access_denied**: 用户拒绝了授权请求
- **登录失败**: 用户在系统中不存在或产品ID不匹配

### 6.2 调试建议

1. 检查 Google Cloud Console 中的 OAuth 配置
2. 验证应用程序配置文件中的参数
3. 查看服务器日志获取详细错误信息
4. 使用浏览器开发者工具检查网络请求

## 7. 配置示例

### 7.1 开发环境配置

```yaml
google:
  oauth:
    client-id: "262110376311-cstpq2vvfib88puap3m1gsfu0veaaea6.apps.googleusercontent.com"
    client-secret: "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"
    redirect-uri: "http://localhost:3001/auth/google/callback"
```

### 7.2 生产环境配置

```yaml
google:
  oauth:
    client-id: "${GOOGLE_CLIENT_ID}"
    client-secret: "${GOOGLE_CLIENT_SECRET}"
    redirect-uri: "${GOOGLE_REDIRECT_URI}"
```

使用环境变量：
```bash
export GOOGLE_CLIENT_ID="your-production-client-id"
export GOOGLE_CLIENT_SECRET="your-production-client-secret"
export GOOGLE_REDIRECT_URI="https://yourdomain.com/auth/google/callback"
```
