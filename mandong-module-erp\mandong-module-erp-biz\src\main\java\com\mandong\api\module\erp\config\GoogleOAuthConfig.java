package com.mandong.api.module.erp.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Google Identity Services 配置
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "google.oauth")
public class GoogleOAuthConfig {

    /**
     * Google OAuth 客户端ID
     */
    private String clientId = "262110376311-cstpq2vvfib88puap3m1gsfu0veaaea6.apps.googleusercontent.com";

    /**
     * Google OAuth 客户端密钥（对于ID Token验证，可选）
     */
    private String clientSecret = "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";

    /**
     * 重定向URI（Google Identity Services 不需要）
     */
    private String redirectUri = "";
}
