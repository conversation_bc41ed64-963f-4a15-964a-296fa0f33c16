package com.mandong.api.module.erp.controller.admin.department;

import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.department.vo.*;
import com.mandong.api.module.erp.dal.dataobject.department.DepartmentDO;
import com.mandong.api.module.erp.service.department.DepartmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mandong.api.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - SDK部门管理")
@RestController
@RequestMapping("/erp/department")
@Validated
@Slf4j
public class DepartmentController {

    @Resource
    private DepartmentService departmentService;

    @PostMapping("/create")
    @Operation(summary = "创建部门")
    @PreAuthorize("@ss.hasPermission('erp:department:create')")
    public CommonResult<Long> createDepartment(@Valid @RequestBody DepartmentSaveReqVO createReqVO) {
        return success(departmentService.createDepartment(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新部门")
    @PreAuthorize("@ss.hasPermission('erp:department:update')")
    public CommonResult<Boolean> updateDepartment(@Valid @RequestBody DepartmentSaveReqVO updateReqVO) {
        departmentService.updateDepartment(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除部门")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('erp:department:delete')")
    public CommonResult<Boolean> deleteDepartment(@RequestParam("id") Long id) {
        departmentService.deleteDepartment(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得部门")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('erp:department:query')")
    public CommonResult<DepartmentDO> getDepartment(@RequestParam("id") Long id) {
        DepartmentDO department = departmentService.getDepartment(id);
        return success(department);
    }

    @GetMapping("/page")
    @Operation(summary = "获得部门分页")
    @PreAuthorize("@ss.hasPermission('erp:department:query')")
    public CommonResult<PageResult<DepartmentRespVO>> getDepartmentPage(@Valid DepartmentPageReqVO pageReqVO) {
        PageResult<DepartmentRespVO> pageResult = departmentService.getDepartmentPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/tree")
    @Operation(summary = "获得部门树形结构")
    @PreAuthorize("@ss.hasPermission('erp:department:query')")
    public CommonResult<List<DepartmentRespVO>> getDepartmentTree() {
        List<DepartmentRespVO> list = departmentService.getDepartmentTree();
        return success(list);
    }

    @GetMapping("/list")
    @Operation(summary = "获得部门列表")
    @PreAuthorize("@ss.hasPermission('erp:department:query')")
    public CommonResult<List<DepartmentDO>> getDepartmentList(@Valid DepartmentPageReqVO reqVO) {
        List<DepartmentDO> list = departmentService.getDepartmentList(reqVO);
        return success(list);
    }

    @PostMapping("/set-user-departments")
    @Operation(summary = "设置用户部门关系")
    @PreAuthorize("@ss.hasPermission('erp:department:update')")
    public CommonResult<Boolean> setUserDepartments(@Valid @RequestBody UserDeptRelationVO relationVO) {
        departmentService.setUserDepartments(relationVO);
        return success(true);
    }

    @PostMapping("/assign-user")
    @Operation(summary = "分配用户到部门")
    @PreAuthorize("@ss.hasPermission('erp:department:update')")
    public CommonResult<Boolean> assignUserToDepartment(@Valid @RequestBody UserDeptRelationVO relationVO) {
        departmentService.setUserDepartments(relationVO);
        return success(true);
    }

    @GetMapping("/get-user-departments")
    @Operation(summary = "获取用户的部门列表")
    @Parameter(name = "userId", description = "用户ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('erp:department:query')")
    public CommonResult<List<DepartmentRespVO>> getUserDepartments(@RequestParam("userId") Long userId) {
        List<DepartmentRespVO> list = departmentService.getUserDepartments(userId);
        return success(list);
    }

    @DeleteMapping("/remove-user-from-department")
    @Operation(summary = "移除用户部门关系")
    @Parameter(name = "userId", description = "用户ID", required = true, example = "1")
    @Parameter(name = "deptId", description = "部门ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('erp:department:delete')")
    public CommonResult<Boolean> removeUserFromDepartment(@RequestParam("userId") Long userId, 
                                                          @RequestParam("deptId") Long deptId) {
        departmentService.removeUserFromDepartment(userId, deptId);
        return success(true);
    }

    @GetMapping("/users")
    @Operation(summary = "获取部门下的用户列表")
    @Parameter(name = "deptId", description = "部门ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('erp:department:query')")
    public CommonResult<List<UserDeptRelationVO>> getDepartmentUsers(@RequestParam("deptId") Long deptId) {
        List<UserDeptRelationVO> list = departmentService.getDepartmentUsers(deptId);
        return success(list);
    }
} 