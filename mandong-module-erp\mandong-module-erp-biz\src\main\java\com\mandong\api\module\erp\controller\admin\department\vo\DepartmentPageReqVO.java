package com.mandong.api.module.erp.controller.admin.department.vo;

import com.mandong.api.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 部门分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DepartmentPageReqVO extends PageParam {

    @Schema(description = "部门名称", example = "技术部")
    private String deptName;

    @Schema(description = "部门编码", example = "TECH")
    private String deptCode;

    @Schema(description = "部门层级", example = "1")
    private Integer deptLevel;

    @Schema(description = "父部门ID", example = "1")
    private Long parentId;

    @Schema(description = "状态", example = "1")
    private Integer status;
} 