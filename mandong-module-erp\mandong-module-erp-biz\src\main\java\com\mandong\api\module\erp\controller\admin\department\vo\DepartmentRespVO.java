package com.mandong.api.module.erp.controller.admin.department.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 部门 Response VO")
@Data
public class DepartmentRespVO {

    @Schema(description = "部门ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "父部门ID", example = "0")
    private Long parentId;

    @Schema(description = "部门名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "技术部")
    private String deptName;

    @Schema(description = "部门编码", example = "TECH")
    private String deptCode;

    @Schema(description = "部门层级", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer deptLevel;

    @Schema(description = "部门层级描述", example = "主管部门")
    private String deptLevelDesc;

    @Schema(description = "部门业务类型", example = "OPERATION")
    private String deptType;

    @Schema(description = "部门类型描述", example = "运营部门")
    private String deptTypeDesc;

    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "状态描述", example = "启用")
    private String statusDesc;

    @Schema(description = "部门描述", example = "负责技术研发工作")
    private String description;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "子部门列表")
    private List<DepartmentRespVO> children;

    @Schema(description = "部门用户数量", example = "5")
    private Integer userCount;
} 