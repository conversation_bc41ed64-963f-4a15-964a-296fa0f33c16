package com.mandong.api.module.erp.controller.admin.department.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Schema(description = "管理后台 - 部门新增/修改 Request VO")
@Data
public class DepartmentSaveReqVO {

    @Schema(description = "部门ID", example = "1")
    private Long id;

    @Schema(description = "父部门ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "父部门ID不能为空")
    private Long parentId;

    @Schema(description = "部门名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "技术部")
    @NotBlank(message = "部门名称不能为空")
    private String deptName;

    @Schema(description = "部门编码", example = "TECH")
    private String deptCode;

    @Schema(description = "部门层级", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "部门层级不能为空")
    private Integer deptLevel;

    @Schema(description = "部门业务类型", example = "OPERATION")
    private String deptType;

    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "部门描述", example = "负责技术研发工作")
    private String description;
} 