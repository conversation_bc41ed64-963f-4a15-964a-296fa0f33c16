package com.mandong.api.module.erp.controller.admin.department.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 用户部门关系 Request VO")
@Data
public class UserDeptRelationVO {

    @Schema(description = "关系ID", example = "1")
    private Long id;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "部门ID", example = "1")
    private Long deptId;

    @Schema(description = "部门ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "部门ID列表不能为空")
    private List<Long> deptIds;

    @Schema(description = "关系类型", example = "1")
    private Integer relationType;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    // 查询相关字段
    @Schema(description = "用户名", example = "张三")
    private String userName;

    @Schema(description = "部门名称", example = "技术部")
    private String deptName;

    @Schema(description = "角色类型", example = "主管")
    private String roleType;
}