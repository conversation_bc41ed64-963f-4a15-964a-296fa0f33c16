package com.mandong.api.module.erp.controller.admin.game.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Schema(description = "Google 登录请求 VO")
@Data
public class GameGoogleReqVO {

    @Schema(description = "Google ID Token (JWT)", requiredMode = Schema.RequiredMode.REQUIRED, example = "eyJhbGciOiJSUzI1NiIsImtpZCI6...")
    @NotBlank(message = "ID Token 不能为空")
    private String access_token;

    @Schema(description = "Google 用户ID", example = "1234567890")
    private String googleId;

    @Schema(description = "用户邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "用户姓名", example = "张三")
    private String name;

    @Schema(description = "游戏ID", example = "123")
    private Long gameId;

    @Schema(description = "网站Id")
    private Long siteId;

    @Schema(description = "产品ID数组", example = "[\"1\", \"2\"]")
    private String productId;

}
