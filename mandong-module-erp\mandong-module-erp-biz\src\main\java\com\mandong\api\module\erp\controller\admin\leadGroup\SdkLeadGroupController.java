package com.mandong.api.module.erp.controller.admin.leadGroup;

import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.controller.admin.leadGroup.vo.*;
import com.mandong.api.module.erp.dal.dataobject.leadGroup.SdkLeadGroupLinkDO;
import com.mandong.api.module.erp.dto.permission.OrderPermissionDTO;
import com.mandong.api.module.erp.service.leadGroup.SdkLeadGroupService;
import com.mandong.api.module.erp.service.permission.DataPermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.mandong.api.framework.common.pojo.CommonResult.error;
import static com.mandong.api.framework.common.pojo.CommonResult.success;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.LEAD_GROUP_LINKS_EXISTS;

@Tag(name = "管理后台 - SDK带队归属")
@RestController
@RequestMapping("/erp/leadGroup")
@Validated
public class SdkLeadGroupController {

    @Resource
    private SdkLeadGroupService sdkLeadGroupService;

    @Resource
    private DataPermissionService dataPermissionService;

    @GetMapping("/page")
    @Operation(summary = "获得分页")
    @PreAuthorize("@ss.hasPermission('erp:leadGroup:query')")
    public CommonResult<PageResult<LeadGroupRespVO>> getPage(@Valid LeadGroupPageReqVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        
        // 获取用户权限信息
        OrderPermissionDTO permission = dataPermissionService.getUserOrderPermission(userId);
        
        // 根据权限过滤数据
        PageResult<LeadGroupRespVO> pageResult = sdkLeadGroupService.getLeadGroupPageWithPermission(pageReqVO, permission);
        
        return success(pageResult);
    }

    @GetMapping("/LinkPage")
    @Operation(summary = "获得分页")
    @PreAuthorize("@ss.hasPermission('erp:leadGroup:query')")
    public CommonResult<PageResult<LeadGroupLinksPageRespVO>> getLinkPage(@Valid LeadGroupLinksPageReqVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        
        // 获取用户权限信息
        OrderPermissionDTO permission = dataPermissionService.getUserOrderPermission(userId);
        
        // 根据权限过滤数据
        PageResult<LeadGroupLinksPageRespVO> pageResult = sdkLeadGroupService.getLeadGroupLinksPageWithPermission(pageReqVO, permission);
        
        return success(pageResult);
    }

    @GetMapping("/monthlyStats")
    @Operation(summary = "获得分页")
    @PreAuthorize("@ss.hasPermission('erp:leadGroup:query')")
    public CommonResult<List<LeadGroupMonthlyStatsVO>> getLeadGroupMonthlyStats(@Valid Long id, Long startTime, Long endTime) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        
        // 检查用户是否有权限查看此带队归属的数据
        if (!dataPermissionService.canViewLeadGroupData(userId, id)) {
            return success(List.of());
        }
        
        return success(sdkLeadGroupService.getLeadGroupMonthlyStats(id, startTime, endTime));
    }

    @PostMapping("/add")
    @Operation(summary = "新增")
    @PreAuthorize("@ss.hasPermission('erp:leadGroup:add')")
    public CommonResult<Integer> add(@Valid @RequestBody LeadGroupAddReqVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        
        // 检查用户是否有权限创建带队归属
        if (!dataPermissionService.canManageLeadGroupData(userId)) {
            return error(new com.mandong.api.framework.common.exception.ErrorCode(403, "无权限创建带队归属"));
        }
        
        return success(sdkLeadGroupService.add(pageReqVO));
    }

    @PostMapping("/createGroupLinks")
    @Operation(summary = "新增")
    @PreAuthorize("@ss.hasPermission('erp:leadGroup:add')")
    public CommonResult<Integer> addLeadGroupLink(@Valid @RequestBody LeadGroupLinkAddReqVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        
        // 检查用户是否有权限管理带队归属关联
        if (!dataPermissionService.canManageLeadGroupData(userId)) {
            return error(new com.mandong.api.framework.common.exception.ErrorCode(403, "无权限管理带队归属关联"));
        }
        
        return success(sdkLeadGroupService.createGroupLinks(pageReqVO));
    }

    @GetMapping("/deleteGroupLinks")
    @Operation(summary = "删除")
    @PreAuthorize("@ss.hasPermission('erp:leadGroup:delete')")
    public CommonResult<Integer> deleteGroupLinks(@RequestParam("id") Long id) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        
        // 检查用户是否有权限删除带队归属关联
        if (!dataPermissionService.canManageLeadGroupData(userId)) {
            return error(new com.mandong.api.framework.common.exception.ErrorCode(403, "无权限删除带队归属关联"));
        }
        
        return success(sdkLeadGroupService.deleteGroupLinks(id));
    }

    @GetMapping("/delete")
    @Operation(summary = "删除")
    @PreAuthorize("@ss.hasPermission('erp:leadGroup:delete')")
    public CommonResult<Integer> delete(@RequestParam("id") Long id) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        
        // 检查用户是否有权限删除带队归属
        if (!dataPermissionService.canManageLeadGroupData(userId)) {
            return error(new com.mandong.api.framework.common.exception.ErrorCode(403, "无权限删除带队归属"));
        }
        
        List<SdkLeadGroupLinkDO> leadGroupLinks = sdkLeadGroupService.getLeadGroupLinks(id);
        if (!leadGroupLinks.isEmpty()) {
            return error(LEAD_GROUP_LINKS_EXISTS);
        }

        return success(sdkLeadGroupService.delete(id));
    }

    @GetMapping("/current-user-permission")
    @Operation(summary = "获取当前用户权限信息")
    @PreAuthorize("@ss.hasPermission('erp:leadGroup:query')")
    public CommonResult<OrderPermissionDTO> getCurrentUserPermission() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        OrderPermissionDTO permission = dataPermissionService.getUserOrderPermission(userId);
        return success(permission);
    }
}
