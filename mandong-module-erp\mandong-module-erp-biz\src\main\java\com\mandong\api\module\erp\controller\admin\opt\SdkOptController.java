package com.mandong.api.module.erp.controller.admin.opt;

import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.controller.admin.opt.vo.*;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptLinkDO;
import com.mandong.api.module.erp.dto.permission.OrderPermissionDTO;
import com.mandong.api.module.erp.service.opt.SdkOptService;
import com.mandong.api.module.erp.service.permission.DataPermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.mandong.api.framework.common.pojo.CommonResult.error;
import static com.mandong.api.framework.common.pojo.CommonResult.success;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.LEAD_GROUP_LINKS_EXISTS;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.OPT_LINKS_EXISTS;

@Tag(name = "管理后台 - SDK运维管理")
@RestController
@RequestMapping("/erp/opt")
@Validated
public class SdkOptController {

    @Resource
    private SdkOptService sdkOptService;

    @Resource
    private DataPermissionService dataPermissionService;

    @GetMapping("/page")
    @Operation(summary = "获得分页")
    @PreAuthorize("@ss.hasAnyPermissions('erp:opt:query','erp:leadGroup:query')")
    public CommonResult<PageResult<OptRespVO>> getPage(@Valid OptPageReqVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        
        // 获取用户权限信息
        OrderPermissionDTO permission = dataPermissionService.getUserOrderPermission(userId);
        
        // 根据权限过滤数据
        PageResult<OptRespVO> pageResult = sdkOptService.getOptPageWithPermission(pageReqVO, permission);
        
        return success(pageResult);
    }

    @GetMapping("/LinkPage")
    @Operation(summary = "获得分页")
    @PreAuthorize("@ss.hasPermission('erp:opt:query')")
    public CommonResult<PageResult<OptLinksPageRespVO>> getLinkPage(@Valid OptLinksPageReqVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        
        // 获取用户权限信息
        OrderPermissionDTO permission = dataPermissionService.getUserOrderPermission(userId);
        
        // 根据权限过滤数据
        PageResult<OptLinksPageRespVO> pageResult = sdkOptService.getOptLinksPageWithPermission(pageReqVO, permission);
        
        return success(pageResult);
    }

    @GetMapping("/monthlyStats")
    @Operation(summary = "获得分页")
    @PreAuthorize("@ss.hasPermission('erp:opt:query')")
    public CommonResult<List<OptMonthlyStatsVO>> getOptMonthlyStats(@Valid Long id ,Long  startTime,Long  endTime) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        
        // 检查用户是否有权限查看此运维组的数据
        if (!dataPermissionService.canViewOptData(userId, id)) {
            return success(List.of());
        }
        
        return success(sdkOptService.getOptMonthlyStats(id, startTime, endTime));
    }

    @PostMapping("/add")
    @Operation(summary = "新增")
    @PreAuthorize("@ss.hasPermission('erp:opt:add')")
    public CommonResult<Integer> add(@Valid @RequestBody OptAddReqVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        
        // 检查用户是否有权限创建运维组
        if (!dataPermissionService.canManageOptData(userId)) {
            return error(new com.mandong.api.framework.common.exception.ErrorCode(403, "无权限创建运维组"));
        }
        
        return success(sdkOptService.add(pageReqVO));
    }

    @PostMapping("/createGroupLinks")
    @Operation(summary = "新增")
    @PreAuthorize("@ss.hasPermission('erp:opt:add')")
    public CommonResult<Integer> addOptLink(@Valid @RequestBody OptLinkAddReqVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        
        // 检查用户是否有权限管理运维组关联
        if (!dataPermissionService.canManageOptData(userId)) {
            return error(new com.mandong.api.framework.common.exception.ErrorCode(403, "无权限管理运维组关联"));
        }
        
        return success(sdkOptService.createGroupLinks(pageReqVO));
    }

    @GetMapping("/deleteGroupLinks")
    @Operation(summary = "删除")
    @PreAuthorize("@ss.hasPermission('erp:opt:delete')")
    public CommonResult<Integer> deleteGroupLinks(@RequestParam("id") Long id) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        
        // 检查用户是否有权限删除运维组关联
        if (!dataPermissionService.canManageOptData(userId)) {
            return error(new com.mandong.api.framework.common.exception.ErrorCode(403, "无权限删除运维组关联"));
        }
        
        return success(sdkOptService.deleteGroupLinks(id));
    }

    @GetMapping("/delete")
    @Operation(summary = "删除")
    @PreAuthorize("@ss.hasPermission('erp:opt:delete')")
    public CommonResult<Integer> delete(@RequestParam("id") Long id) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        
        // 检查用户是否有权限删除运维组
        if (!dataPermissionService.canManageOptData(userId)) {
            return error(new com.mandong.api.framework.common.exception.ErrorCode(403, "无权限删除运维组"));
        }
        
        List<SdkOptLinkDO> leadGroupLinks = sdkOptService.getOptLinks(id);
        if (!leadGroupLinks.isEmpty()) {
            return error(OPT_LINKS_EXISTS);
        }

        return success(sdkOptService.delete(id));
    }

    @GetMapping("/current-user-permission")
    @Operation(summary = "获取当前用户权限信息")
    @PreAuthorize("@ss.hasPermission('erp:opt:query')")
    public CommonResult<OrderPermissionDTO> getCurrentUserPermission() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        OrderPermissionDTO permission = dataPermissionService.getUserOrderPermission(userId);
        return success(permission);
    }
}
