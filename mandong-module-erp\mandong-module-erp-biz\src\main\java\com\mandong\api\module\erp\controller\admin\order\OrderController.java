package com.mandong.api.module.erp.controller.admin.order;

import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;
import java.util.stream.Collectors;

import com.mandong.api.framework.common.pojo.PageParam;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import static com.mandong.api.framework.common.pojo.CommonResult.success;

import com.mandong.api.framework.excel.core.util.ExcelUtils;

import com.mandong.api.framework.apilog.core.annotation.ApiAccessLog;
import static com.mandong.api.framework.apilog.core.enums.OperateTypeEnum.*;

import com.mandong.api.module.erp.controller.admin.order.vo.*;
import com.mandong.api.module.erp.dal.dataobject.order.OrderDO;
import com.mandong.api.module.erp.service.order.OrderService;
import com.mandong.api.module.erp.service.order.OrderPermissionIntegrationService;
import com.mandong.api.module.erp.service.permission.DataPermissionService;
import com.mandong.api.module.erp.dto.permission.OrderPermissionDTO;

@Tag(name = "管理后台 - 订单")
@RestController
@RequestMapping("/erp/order")
@Validated
public class OrderController {

    @Resource
    private OrderService orderService;

    @Resource
    private OrderPermissionIntegrationService orderPermissionIntegrationService;

    @Resource
    private DataPermissionService dataPermissionService;

    @GetMapping("/get")
    @Operation(summary = "获得订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('erp:order:query')")
    public CommonResult<OrderRespVO> getOrder(@RequestParam("id") Integer id) {
        OrderDO order = orderService.getOrder(id);
        return success(BeanUtils.toBean(order, OrderRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得订单分页")
    @PreAuthorize("@ss.hasPermission('erp:order:query')")
    public CommonResult<PageResult<SdkOrderRespVo>> getOrderPage(@Valid OrderPageReqVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        PageResult<SdkOrderRespVo> pageResult = orderPermissionIntegrationService.getOrderPageWithPermission(userId, pageReqVO);
        return success(pageResult);
    }

    @GetMapping("pageGetTotal")
    @Operation(summary = "获得订单分页金额")
    @PreAuthorize("@ss.hasPermission('erp:order:query')")
    public CommonResult<SdkOrderTotalAmountRespVo> getOrderPageTotalAmount(@Valid OrderPageReqVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        SdkOrderTotalAmountRespVo totalAmount = orderPermissionIntegrationService.getOrderPageTotalAmountWithPermission(userId, pageReqVO);
        return success(totalAmount);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出订单 Excel")
    @PreAuthorize("@ss.hasPermission('erp:order:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportOrderExcel(@Valid OrderPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        
        // 使用权限控制的订单查询
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SdkOrderRespVo> list = orderPermissionIntegrationService.getOrderPageWithPermission(userId, pageReqVO).getList();
        
        // 手动转换时间戳
        List<SdkOrderExportRespVo> exportList = list.stream().map(item -> {
            SdkOrderExportRespVo exportVo = BeanUtils.toBean(item, SdkOrderExportRespVo.class);
            if (item.getPayTime() != null) {
                exportVo.setPayTime(item.getPayTime());
            }
            if (item.getCreateTime() != null) {
                exportVo.setCreateTime(item.getCreateTime());
            }
            return exportVo;
        }).collect(Collectors.toList());
        
        // 导出 Excel
        ExcelUtils.write(response, "订单.xls", "数据", SdkOrderExportRespVo.class, exportList);
    }

    @GetMapping("GetPaySummary")
    @Operation(summary = "获取erp首页数据图标数据")
    @PreAuthorize("@ss.hasPermission('erp:statistics:query')")
    public CommonResult<SdkOrderSummaryRespVO> getOrderPaySummary(@Valid SdkOrderSummaryReqVO reqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        SdkOrderSummaryRespVO summary = orderPermissionIntegrationService.getOrderPaySummaryWithPermission(userId, reqVO);
        return success(summary);
    }

    @PostMapping("GetServer")
    @Operation(summary = "获得区服")
    public CommonResult<List<String>> getServerByProductId(@RequestBody List<Long> productId) {
        return success(orderService.getServerByProductId(productId));
    }

    @GetMapping("/current-user-permission")
    @Operation(summary = "获取当前用户权限信息")
    @PreAuthorize("@ss.hasPermission('erp:order:query')")
    public CommonResult<OrderPermissionDTO> getCurrentUserPermission() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        OrderPermissionDTO permission = dataPermissionService.getUserOrderPermission(userId);
        return success(permission);
    }
}