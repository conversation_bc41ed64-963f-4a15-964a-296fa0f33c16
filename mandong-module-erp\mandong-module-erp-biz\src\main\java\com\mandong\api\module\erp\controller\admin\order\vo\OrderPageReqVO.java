package com.mandong.api.module.erp.controller.admin.order.vo;

import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.mandong.api.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.mandong.api.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 订单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderPageReqVO extends PageParam {

    @Schema(description = "用户名", example = "赵六")
    private String username;

    @Schema(description = "订单号", example = "22173")
    private String orderNo;


    @Schema(description = "运营人员")
    private List<String> channelName;

    @Schema(description = "游戏id", example = "12")
    private List<Long> productId;
    
    private List<String> channelCode;

    @Schema(description = "角色id", example = "22926")
    private String roleId;

    @Schema(description = "区服")
    private String serverName;

    @Schema(description = "角色名", example = "李四")
    private String roleName;

    @Schema(description = "支付方式")
    private String payName;

    private List<String> servers;


    @Schema(description = "支付方式")
    private String payment;

    @Schema(description = "交易时间")
    private Long[] payTime;

    @Schema(description = "创建时间")
    private Long[] createTime ;

    @Schema(description = "订单状态", example = "1")
    private String payStatus;
    @Schema(description = "用户id", example = "1")
    private Long uid;


    @Schema(description = "订单来源（1--龙成后台  2---sdk后台)")
    private Integer orderSource;

    private List<Long> userIds;





}