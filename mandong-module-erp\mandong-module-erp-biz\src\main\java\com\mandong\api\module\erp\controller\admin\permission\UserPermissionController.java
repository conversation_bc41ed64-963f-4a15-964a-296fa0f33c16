package com.mandong.api.module.erp.controller.admin.permission;

import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.controller.admin.permission.vo.UserPermissionMappingVO;
import com.mandong.api.module.erp.dto.permission.OrderPermissionDTO;
import com.mandong.api.module.erp.dto.permission.UserHierarchyDTO;
import com.mandong.api.module.erp.service.permission.DataPermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mandong.api.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - SDK用户权限管理")
@RestController
@RequestMapping("/erp/user-permission")
@Validated
@Slf4j
public class UserPermissionController {

    @Resource
    private DataPermissionService dataPermissionService;

    @GetMapping("/get-user-order-permission")
    @Operation(summary = "获取用户订单查询权限")
    @Parameter(name = "userId", description = "用户ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('erp:permission:query')")
    public CommonResult<OrderPermissionDTO> getUserOrderPermission(@RequestParam("userId") Long userId) {
        OrderPermissionDTO permission = dataPermissionService.getUserOrderPermission(userId);
        return success(permission);
    }

    @GetMapping("/get-current-user-permission")
    @Operation(summary = "获取当前用户权限信息")
    @PreAuthorize("@ss.hasPermission('erp:permission:query')")
    public CommonResult<OrderPermissionDTO> getCurrentUserPermission() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        OrderPermissionDTO permission = dataPermissionService.getUserOrderPermission(userId);
        return success(permission);
    }

    @GetMapping("/get-user-hierarchy")
    @Operation(summary = "获取用户层级信息")
    @Parameter(name = "userId", description = "用户ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('erp:permission:query')")
    public CommonResult<UserHierarchyDTO> getUserHierarchy(@RequestParam("userId") Long userId) {
        UserHierarchyDTO hierarchy = dataPermissionService.getUserHierarchyInfo(userId);
        return success(hierarchy);
    }

    @GetMapping("/get-current-user-hierarchy")
    @Operation(summary = "获取当前用户层级信息")
    @PreAuthorize("@ss.hasPermission('erp:permission:query')")
    public CommonResult<UserHierarchyDTO> getCurrentUserHierarchy() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        UserHierarchyDTO hierarchy = dataPermissionService.getUserHierarchyInfo(userId);
        return success(hierarchy);
    }

    @GetMapping("/get-user-accessible-products")
    @Operation(summary = "获取用户可访问的产品ID列表")
    @Parameter(name = "userId", description = "用户ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('erp:permission:query')")
    public CommonResult<List<Long>> getUserAccessibleProducts(@RequestParam("userId") Long userId) {
        List<Long> productIds = dataPermissionService.getUserAccessibleProductIds(userId);
        return success(productIds);
    }

    @GetMapping("/get-current-user-accessible-products")
    @Operation(summary = "获取当前用户可访问的产品ID列表")
    @PreAuthorize("@ss.hasPermission('erp:permission:query')")
    public CommonResult<List<Long>> getCurrentUserAccessibleProducts() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        List<Long> productIds = dataPermissionService.getUserAccessibleProductIds(userId);
        return success(productIds);
    }

    @PostMapping("/check-order-permission")
    @Operation(summary = "检查用户是否有权限查看指定订单")
    @PreAuthorize("@ss.hasPermission('erp:permission:query')")
    public CommonResult<Boolean> checkOrderPermission(@RequestParam("userId") Long userId,
                                                      @RequestParam("productId") Long productId,
                                                      @RequestParam("channelCode") String channelCode,
                                                      @RequestParam("serverName") String serverName) {
        boolean canView = dataPermissionService.canViewOrder(userId, productId, channelCode, serverName);
        return success(canView);
    }


} 