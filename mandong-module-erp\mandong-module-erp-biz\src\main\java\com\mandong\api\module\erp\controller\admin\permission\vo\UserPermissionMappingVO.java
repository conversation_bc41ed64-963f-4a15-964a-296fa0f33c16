package com.mandong.api.module.erp.controller.admin.permission.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 用户权限映射 Request VO")
@Data
public class UserPermissionMappingVO {

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "资源类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "PRODUCT")
    @NotNull(message = "资源类型不能为空")
    private String resourceType;

    @Schema(description = "资源ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "资源ID列表不能为空")
    private List<String> resourceIds;

    @Schema(description = "权限类型", example = "READ")
    private String permissionType;

    @Schema(description = "状态", example = "1")
    private Integer status;

    // 查询用字段
    @Schema(description = "用户名", example = "张三")
    private String userName;

    @Schema(description = "资源名称", example = "产品A")
    private String resourceName;
} 