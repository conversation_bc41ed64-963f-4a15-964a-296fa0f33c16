package com.mandong.api.module.erp.controller.admin.report;

import com.mandong.api.framework.apilog.core.annotation.ApiAccessLog;
import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageParam;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.framework.excel.core.util.ExcelUtils;
import com.mandong.api.module.erp.controller.admin.order.vo.*;
import com.mandong.api.module.erp.controller.admin.report.vo.OptSalePriceVO;
import com.mandong.api.module.erp.dal.dataobject.order.OrderDO;
import com.mandong.api.module.erp.service.order.OrderService;
import com.mandong.api.module.erp.service.report.ReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import static com.mandong.api.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.mandong.api.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 订单")
@RestController
@RequestMapping("/report")
@Validated
public class ReportController {



    @Resource
    private ReportService reportService;





    @GetMapping("/GetOptSalePrice")
    @Operation(summary = "获取erp首页数据图标数据")
    @PreAuthorize("@ss.hasPermission('erp:statistics:query')")
    public CommonResult<List<OptSalePriceVO>> GetOptSalePrice(@Valid SdkOrderSummaryReqVO reqVO) {

        return success(reportService.GetOptSalePrice(reqVO));
    }




}