package com.mandong.api.module.erp.controller.admin.resourcebinding;

import com.mandong.api.module.erp.controller.admin.resourcebinding.vo.BatchResourceBindingReqVO;
import com.mandong.api.module.erp.controller.admin.resourcebinding.vo.UserResourceBindingVO;
import com.mandong.api.module.erp.service.resourcebinding.UserResourceBindingService;
import com.mandong.api.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mandong.api.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 资源绑定管理
 */
@Tag(name = "管理后台 - 资源绑定管理")
@RestController
@RequestMapping("/erp/resource-binding")
@Validated
@Slf4j
public class ResourceBindingController {

    @Resource
    private UserResourceBindingService userResourceBindingService;

    @GetMapping("/user-bindings")
    @Operation(summary = "获取用户资源绑定列表")
    @Parameter(name = "userId", description = "用户ID", required = true)
    public CommonResult<List<UserResourceBindingVO>> getUserResourceBinding(@RequestParam("userId") Long userId) {
        List<UserResourceBindingVO> list = userResourceBindingService.getUserResourceBinding(userId);
        return success(list);
    }

    @PostMapping("/batch-set")
    @Operation(summary = "批量设置用户资源绑定")
    public CommonResult<Boolean> batchSetUserResourceBinding(@Valid @RequestBody BatchResourceBindingReqVO reqVO) {
        userResourceBindingService.batchSetUserResourceBinding(reqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户资源绑定")
    @Parameter(name = "id", description = "绑定ID", required = true)
    public CommonResult<Boolean> deleteUserResourceBinding(@RequestParam("id") Long id) {
        userResourceBindingService.deleteUserResourceBinding(id);
        return success(true);
    }

    @GetMapping("/accessible-bindings")
    @Operation(summary = "获取用户可访问的资源绑定")
    @Parameter(name = "userId", description = "用户ID", required = true)
    public CommonResult<List<UserResourceBindingVO>> getAccessibleBindings(@RequestParam("userId") Long userId) {
        List<UserResourceBindingVO> list = userResourceBindingService.getAccessibleBindings(userId);
        return success(list);
    }

    @GetMapping("/test-permission")
    @Operation(summary = "测试用户权限")
    public CommonResult<Boolean> testUserPermission(
            @RequestParam("userId") Long userId,
            @RequestParam("resourceType") String resourceType,
            @RequestParam("resourceValue") String resourceValue) {
        boolean hasPermission = userResourceBindingService.testUserPermission(userId, resourceType, resourceValue);
        return success(hasPermission);
    }

    @GetMapping("/products")
    @Operation(summary = "获取用户可访问的产品ID列表")
    @Parameter(name = "userId", description = "用户ID", required = true)
    public CommonResult<List<Long>> getUserBoundProductIds(@RequestParam("userId") Long userId) {
        List<Long> list = userResourceBindingService.getUserBoundProductIds(userId);
        return success(list);
    }

    @GetMapping("/channels")
    @Operation(summary = "获取用户可访问的渠道代码列表")
    @Parameter(name = "userId", description = "用户ID", required = true)
    public CommonResult<List<String>> getUserBoundChannelCodes(@RequestParam("userId") Long userId) {
        List<String> list = userResourceBindingService.getUserBoundChannelCodes(userId);
        return success(list);
    }

    @GetMapping("/operation-teams")
    @Operation(summary = "获取用户可访问的运营队伍列表")
    @Parameter(name = "userId", description = "用户ID", required = true)
    public CommonResult<List<String>> getUserBoundOperationTeams(@RequestParam("userId") Long userId) {
        List<String> list = userResourceBindingService.getUserBoundOperationTeams(userId);
        return success(list);
    }

    @GetMapping("/servers")
    @Operation(summary = "获取用户可访问的区服列表")
    @Parameter(name = "userId", description = "用户ID", required = true)
    public CommonResult<List<String>> getUserBoundServerNames(@RequestParam("userId") Long userId) {
        List<String> list = userResourceBindingService.getUserBoundServerNames(userId);
        return success(list);
    }

    @GetMapping("/check-product-access")
    @Operation(summary = "检查用户对指定产品的访问权限")
    public CommonResult<Boolean> hasProductAccess(
            @RequestParam("userId") Long userId,
            @RequestParam("productId") Long productId) {
        boolean hasAccess = userResourceBindingService.hasProductAccess(userId, productId);
        return success(hasAccess);
    }

    @GetMapping("/check-channel-access")
    @Operation(summary = "检查用户对指定渠道的访问权限")
    public CommonResult<Boolean> hasChannelAccess(
            @RequestParam("userId") Long userId,
            @RequestParam("channelCode") String channelCode) {
        boolean hasAccess = userResourceBindingService.hasChannelAccess(userId, channelCode);
        return success(hasAccess);
    }

    @GetMapping("/check-operation-team-access")
    @Operation(summary = "检查用户对指定运营队伍的访问权限")
    public CommonResult<Boolean> hasOperationTeamAccess(
            @RequestParam("userId") Long userId,
            @RequestParam("operationTeam") String operationTeam) {
        boolean hasAccess = userResourceBindingService.hasOperationTeamAccess(userId, operationTeam);
        return success(hasAccess);
    }

    @GetMapping("/check-server-access")
    @Operation(summary = "检查用户对指定区服的访问权限")
    public CommonResult<Boolean> hasServerAccess(
            @RequestParam("userId") Long userId,
            @RequestParam("serverName") String serverName) {
        boolean hasAccess = userResourceBindingService.hasServerAccess(userId, serverName);
        return success(hasAccess);
    }
} 