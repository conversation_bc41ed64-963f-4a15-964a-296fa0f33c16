package com.mandong.api.module.erp.controller.admin.resourcebinding;

import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.controller.admin.resourcebinding.vo.BatchResourceBindingReqVO;
import com.mandong.api.module.erp.controller.admin.resourcebinding.vo.OperationBindingReqVO;
import com.mandong.api.module.erp.controller.admin.resourcebinding.vo.OpsBindingReqVO;
import com.mandong.api.module.erp.service.resourcebinding.UserResourceBindingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;

import static com.mandong.api.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 用户资源绑定管理
 */
@Tag(name = "管理后台 - 用户资源绑定管理")
@RestController
@RequestMapping("/erp/user-resource")
@Validated
@Slf4j
public class UserResourceController {

    @Resource
    private UserResourceBindingService userResourceBindingService;

    @PostMapping("/save-operation-binding")
    @Operation(summary = "保存运营部门用户资源绑定")
    @PreAuthorize("@ss.hasPermission('erp:department:update')")
    public CommonResult<Boolean> saveOperationBinding(@Valid @RequestBody OperationBindingReqVO reqVO) {
        log.info("保存运营部门用户资源绑定: userId={}, deptId={}, bindingMode={}, productIds={}, channelCodes={}, bindingItems={}", 
                reqVO.getUserId(), reqVO.getDeptId(), reqVO.getBindingMode(), 
                reqVO.getProductIds(), reqVO.getChannelCodes(), reqVO.getBindingItems());
        
        try {
            // 构建批量绑定请求
            BatchResourceBindingReqVO batchReqVO = new BatchResourceBindingReqVO();
            batchReqVO.setUserId(reqVO.getUserId());
            batchReqVO.setOperationMode("ADD"); // 使用增量添加模式，保留现有绑定
            
            List<BatchResourceBindingReqVO.ResourceBindingItemVO> bindings = new ArrayList<>();
            
            // 优先使用bindingItems（详细的绑定项）
            if (reqVO.getBindingItems() != null && !reqVO.getBindingItems().isEmpty()) {
                // 使用详细的绑定项
                for (OperationBindingReqVO.ProductChannelBindingItem item : reqVO.getBindingItems()) {
                    BatchResourceBindingReqVO.ResourceBindingItemVO bindingItem = 
                        new BatchResourceBindingReqVO.ResourceBindingItemVO();
                    bindingItem.setSelectedProductIds(List.of(item.getProductId()));
                    bindingItem.setSelectedChannelCodes(List.of(item.getChannelCode()));
                    bindingItem.setStatus(reqVO.getStatus());
                    bindingItem.setRemark(reqVO.getRemark());
                    bindings.add(bindingItem);
                    
                    log.debug("创建产品渠道绑定: productId={}, channelCode={}, productName={}, channelName={}", 
                            item.getProductId(), item.getChannelCode(), item.getProductName(), item.getChannelName());
                }
            } else {
                // 使用传统的产品和渠道数组
                String bindingMode = reqVO.getBindingMode() != null ? reqVO.getBindingMode() : "ONE_TO_ONE";
                
                if ("CARTESIAN".equals(bindingMode)) {
                    // 笛卡尔积全组合模式
                    log.info("使用笛卡尔积全组合模式");
                    for (Long productId : reqVO.getProductIds()) {
                        for (String channelCode : reqVO.getChannelCodes()) {
                            BatchResourceBindingReqVO.ResourceBindingItemVO bindingItem = 
                                new BatchResourceBindingReqVO.ResourceBindingItemVO();
                            bindingItem.setSelectedProductIds(List.of(productId));
                            bindingItem.setSelectedChannelCodes(List.of(channelCode));
                            bindingItem.setStatus(reqVO.getStatus());
                            bindingItem.setRemark(reqVO.getRemark());
                            bindings.add(bindingItem);
                            
                            log.debug("创建产品渠道绑定: productId={}, channelCode={}", productId, channelCode);
                        }
                    }
                } else {
                    // 一一对应模式
                    log.info("使用一一对应模式");
                    // 验证产品和渠道数量是否匹配
                    if (reqVO.getProductIds().size() != reqVO.getChannelCodes().size()) {
                        log.error("产品数量和渠道数量不匹配: productIds={}, channelCodes={}", 
                                reqVO.getProductIds().size(), reqVO.getChannelCodes().size());
                        throw new IllegalArgumentException("产品和渠道必须一一对应，数量不匹配");
                    }
                    
                    // 按数组索引一一对应创建绑定
                    for (int i = 0; i < reqVO.getProductIds().size(); i++) {
                        Long productId = reqVO.getProductIds().get(i);
                        String channelCode = reqVO.getChannelCodes().get(i);
                        
                        BatchResourceBindingReqVO.ResourceBindingItemVO bindingItem = 
                            new BatchResourceBindingReqVO.ResourceBindingItemVO();
                        bindingItem.setSelectedProductIds(List.of(productId));
                        bindingItem.setSelectedChannelCodes(List.of(channelCode));
                        bindingItem.setStatus(reqVO.getStatus());
                        bindingItem.setRemark(reqVO.getRemark());
                        bindings.add(bindingItem);
                        
                        log.debug("创建产品渠道绑定: productId={}, channelCode={}", productId, channelCode);
                    }
                }
            }
            
            batchReqVO.setBindings(bindings);
            userResourceBindingService.batchSetUserResourceBinding(batchReqVO);
            
            log.info("运营部门用户资源绑定保存成功: userId={}, bindingCount={}", reqVO.getUserId(), bindings.size());
            return success(true);
        } catch (Exception e) {
            log.error("保存运营部门用户资源绑定失败: userId={}, error={}", reqVO.getUserId(), e.getMessage(), e);
            throw e;
        }
    }

    @PostMapping("/save-ops-binding")
    @Operation(summary = "保存运维部门用户资源绑定")
    @PreAuthorize("@ss.hasPermission('erp:department:update')")
    public CommonResult<Boolean> saveOpsBinding(@Valid @RequestBody OpsBindingReqVO reqVO) {
        log.info("保存运维部门用户资源绑定: userId={}, deptId={}, targetDeptId={}, bindingMode={}, teams={}, productIds={}, serverNames={}, bindingItems={}", 
                reqVO.getUserId(), reqVO.getDeptId(), reqVO.getTargetDeptId(), reqVO.getBindingMode(), 
                reqVO.getTeams(), reqVO.getProductIds(), reqVO.getServerNames(), reqVO.getBindingItems());
        
        try {
            // 构建批量绑定请求
            BatchResourceBindingReqVO batchReqVO = new BatchResourceBindingReqVO();
            batchReqVO.setUserId(reqVO.getUserId());
            batchReqVO.setOperationMode("ADD"); // 使用增量添加模式，保留现有绑定
            
            List<BatchResourceBindingReqVO.ResourceBindingItemVO> bindings = new ArrayList<>();
            
            // 为每个运营队伍创建绑定
            for (String team : reqVO.getTeams()) {
                BatchResourceBindingReqVO.ResourceBindingItemVO bindingItem = 
                    new BatchResourceBindingReqVO.ResourceBindingItemVO();
                bindingItem.setOperationTeams(List.of(team));
                bindingItem.setTargetDeptId(reqVO.getTargetDeptId());
                bindingItem.setOpsDeptId(reqVO.getDeptId());
                bindingItem.setStatus(reqVO.getStatus());
                bindingItem.setRemark(reqVO.getRemark());
                bindings.add(bindingItem);
            }
            
            // 处理产品和区服绑定
            // 优先使用bindingItems（详细的绑定项）
            if (reqVO.getBindingItems() != null && !reqVO.getBindingItems().isEmpty()) {
                // 使用详细的绑定项
                for (OpsBindingReqVO.ProductServerBindingItem item : reqVO.getBindingItems()) {
                    BatchResourceBindingReqVO.ResourceBindingItemVO bindingItem = 
                        new BatchResourceBindingReqVO.ResourceBindingItemVO();
                    bindingItem.setSelectedProductIds(List.of(item.getProductId()));
                    bindingItem.setServerNames(List.of(item.getServerName()));
                    bindingItem.setTargetDeptId(reqVO.getTargetDeptId());
                    bindingItem.setOpsDeptId(reqVO.getDeptId());
                    bindingItem.setStatus(reqVO.getStatus());
                    bindingItem.setRemark(reqVO.getRemark());
                    bindings.add(bindingItem);
                    
                    log.debug("创建产品区服绑定: productId={}, serverName={}, productName={}", 
                            item.getProductId(), item.getServerName(), item.getProductName());
                }
            } else {
                // 使用传统的产品和区服数组
                String bindingMode = reqVO.getBindingMode() != null ? reqVO.getBindingMode() : "ONE_TO_ONE";
                
                if ("CARTESIAN".equals(bindingMode)) {
                    // 笛卡尔积全组合模式
                    log.info("使用笛卡尔积全组合模式");
                    for (Long productId : reqVO.getProductIds()) {
                        for (String serverName : reqVO.getServerNames()) {
                            BatchResourceBindingReqVO.ResourceBindingItemVO bindingItem = 
                                new BatchResourceBindingReqVO.ResourceBindingItemVO();
                            bindingItem.setSelectedProductIds(List.of(productId));
                            bindingItem.setServerNames(List.of(serverName));
                            bindingItem.setTargetDeptId(reqVO.getTargetDeptId());
                            bindingItem.setOpsDeptId(reqVO.getDeptId());
                            bindingItem.setStatus(reqVO.getStatus());
                            bindingItem.setRemark(reqVO.getRemark());
                            bindings.add(bindingItem);
                            
                            log.debug("创建产品区服绑定: productId={}, serverName={}", productId, serverName);
                        }
                    }
                } else {
                    // 一一对应模式
                    log.info("使用一一对应模式");
                    // 验证产品和区服数量是否匹配
                    if (reqVO.getProductIds().size() != reqVO.getServerNames().size()) {
                        log.error("产品数量和区服数量不匹配: productIds={}, serverNames={}", 
                                reqVO.getProductIds().size(), reqVO.getServerNames().size());
                        throw new IllegalArgumentException("产品和区服必须一一对应，数量不匹配");
                    }
                    
                    // 按数组索引一一对应创建产品和区服绑定
                    for (int i = 0; i < reqVO.getProductIds().size(); i++) {
                        Long productId = reqVO.getProductIds().get(i);
                        String serverName = reqVO.getServerNames().get(i);
                        
                        BatchResourceBindingReqVO.ResourceBindingItemVO bindingItem = 
                            new BatchResourceBindingReqVO.ResourceBindingItemVO();
                        bindingItem.setSelectedProductIds(List.of(productId));
                        bindingItem.setServerNames(List.of(serverName));
                        bindingItem.setTargetDeptId(reqVO.getTargetDeptId());
                        bindingItem.setOpsDeptId(reqVO.getDeptId());
                        bindingItem.setStatus(reqVO.getStatus());
                        bindingItem.setRemark(reqVO.getRemark());
                        bindings.add(bindingItem);
                        
                        log.debug("创建产品区服绑定: productId={}, serverName={}", productId, serverName);
                    }
                }
            }
            
            batchReqVO.setBindings(bindings);
            userResourceBindingService.batchSetUserResourceBinding(batchReqVO);
            
            log.info("运维部门用户资源绑定保存成功: userId={}, bindingCount={}", reqVO.getUserId(), bindings.size());
            return success(true);
        } catch (Exception e) {
            log.error("保存运维部门用户资源绑定失败: userId={}, error={}", reqVO.getUserId(), e.getMessage(), e);
            throw e;
        }
    }

    @PostMapping("/batch-operation")
    @Operation(summary = "批量操作用户资源绑定（支持指定操作模式）")
    @PreAuthorize("@ss.hasPermission('erp:department:update')")
    public CommonResult<Boolean> batchOperationBinding(@Valid @RequestBody BatchResourceBindingReqVO reqVO) {
        log.info("批量操作用户资源绑定: userId={}, operationMode={}, bindingCount={}", 
                reqVO.getUserId(), reqVO.getOperationMode(), reqVO.getBindings().size());
        
        try {
            userResourceBindingService.batchSetUserResourceBinding(reqVO);
            
            String operationText = getOperationText(reqVO.getOperationMode());
            log.info("用户资源绑定{}成功: userId={}, bindingCount={}", operationText, reqVO.getUserId(), reqVO.getBindings().size());
            return success(true);
        } catch (Exception e) {
            log.error("批量操作用户资源绑定失败: userId={}, operationMode={}, error={}", 
                    reqVO.getUserId(), reqVO.getOperationMode(), e.getMessage(), e);
            throw e;
        }
    }

    @PostMapping("/replace-all-bindings")
    @Operation(summary = "替换用户所有资源绑定")
    @PreAuthorize("@ss.hasPermission('erp:department:update')")
    public CommonResult<Boolean> replaceAllBindings(@Valid @RequestBody BatchResourceBindingReqVO reqVO) {
        log.info("替换用户所有资源绑定: userId={}, bindingCount={}", reqVO.getUserId(), reqVO.getBindings().size());
        
        try {
            // 强制设置为替换模式
            reqVO.setOperationMode("REPLACE");
            userResourceBindingService.batchSetUserResourceBinding(reqVO);
            
            log.info("用户资源绑定替换成功: userId={}, bindingCount={}", reqVO.getUserId(), reqVO.getBindings().size());
            return success(true);
        } catch (Exception e) {
            log.error("替换用户资源绑定失败: userId={}, error={}", reqVO.getUserId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取操作文本描述
     */
    private String getOperationText(String operationMode) {
        switch (operationMode) {
            case "REPLACE":
                return "替换";
            case "ADD":
                return "添加";
            case "UPDATE":
                return "更新";
            case "DELETE":
                return "删除";
            default:
                return "操作";
        }
    }
} 