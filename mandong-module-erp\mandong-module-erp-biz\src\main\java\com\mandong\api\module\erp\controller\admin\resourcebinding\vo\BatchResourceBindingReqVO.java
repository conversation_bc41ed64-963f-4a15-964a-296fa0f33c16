package com.mandong.api.module.erp.controller.admin.resourcebinding.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 批量设置用户资源绑定 Request VO")
@Data
public class BatchResourceBindingReqVO {

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "操作模式：REPLACE-替换所有绑定，ADD-增量添加绑定，UPDATE-更新绑定，DELETE-删除绑定", example = "ADD")
    private String operationMode = "REPLACE";

    @Schema(description = "资源绑定列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "资源绑定列表不能为空")
    private List<ResourceBindingItemVO> bindings;

    @Schema(description = "资源绑定项")
    @Data
    public static class ResourceBindingItemVO {

        @Schema(description = "绑定ID，新增时不传", example = "1")
        private Long id;

        @Schema(description = "运维部门ID", example = "10")
        private Long opsDeptId;

        @Schema(description = "资源类型", example = "PRODUCT_CHANNEL")
        private String resourceType;

        @Schema(description = "目标部门ID（运营部门ID）", example = "20")
        private Long targetDeptId;

        // 旧版单个字段（向后兼容）
        @Schema(description = "产品ID", example = "1")
        private Long productId;

        @Schema(description = "渠道代码", example = "ANDROID")
        private String channelCode;

        @Schema(description = "运营队伍名称", example = "A组")
        private String operationTeam;

        @Schema(description = "区服名称", example = "测试服")
        private String serverName;

        // 新版数组字段（支持多选）
        @Schema(description = "选中的产品ID列表", example = "[1, 2, 3]")
        private List<Long> selectedProductIds;

        @Schema(description = "选中的渠道ID列表", example = "[101, 102, 103]")
        private List<Long> selectedChannelIds;

        @Schema(description = "选中的渠道代码列表", example = "[\"IR\", \"ANDROID\", \"IOS\"]")
        private List<String> selectedChannelCodes;

        @Schema(description = "选中的运营队伍列表", example = "[\"A队\", \"B队\", \"C队\"]")
        private List<String> operationTeams;

        @Schema(description = "选中的区服名称列表", example = "[\"1服\", \"2服\", \"3服\"]")
        private List<String> serverNames;

        @Schema(description = "状态：1-启用，0-禁用", example = "1")
        private Integer status;

        @Schema(description = "备注", example = "测试绑定")
        private String remark;
    }
} 