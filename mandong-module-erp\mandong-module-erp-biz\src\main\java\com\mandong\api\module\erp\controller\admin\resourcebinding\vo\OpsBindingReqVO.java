package com.mandong.api.module.erp.controller.admin.resourcebinding.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 运维部门用户资源绑定请求 VO")
@Data
public class OpsBindingReqVO {

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "部门ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    @NotNull(message = "部门ID不能为空")
    private Long deptId;

    @Schema(description = "目标运营部门ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20")
    @NotNull(message = "目标运营部门ID不能为空")
    private Long targetDeptId;

    @Schema(description = "运营队伍列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "运营队伍列表不能为空")
    private List<String> teams;

    @Schema(description = "产品ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "产品ID列表不能为空")
    private List<Long> productIds;

    @Schema(description = "区服名称列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "区服名称列表不能为空")
    private List<String> serverNames;

    @Schema(description = "绑定模式：ONE_TO_ONE-一一对应，CARTESIAN-笛卡尔积全组合", example = "ONE_TO_ONE")
    private String bindingMode = "ONE_TO_ONE";

    @Schema(description = "状态：1-启用，0-禁用", example = "1")
    private Integer status = 1;

    @Schema(description = "备注", example = "运维部门资源绑定")
    private String remark;
    
    /**
     * 一一对应的产品区服绑定项
     */
    @Schema(description = "一一对应的产品区服绑定项列表")
    private List<ProductServerBindingItem> bindingItems;

    @Schema(description = "产品区服绑定项")
    @Data
    public static class ProductServerBindingItem {
        
        @Schema(description = "产品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        @NotNull(message = "产品ID不能为空")
        private Long productId;

        @Schema(description = "区服名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "测试服")
        @NotNull(message = "区服名称不能为空")
        private String serverName;

        @Schema(description = "产品名称", example = "测试游戏")
        private String productName;
    }
} 