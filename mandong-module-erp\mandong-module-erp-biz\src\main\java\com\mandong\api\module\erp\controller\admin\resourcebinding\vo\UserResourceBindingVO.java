package com.mandong.api.module.erp.controller.admin.resourcebinding.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 用户资源绑定 VO")
@Data
public class UserResourceBindingVO {

    @Schema(description = "绑定ID", example = "1")
    private Long id;

    @Schema(description = "用户ID", example = "1001")
    private Long userId;

    @Schema(description = "运维部门ID", example = "10")
    private Long opsDeptId;

    @Schema(description = "资源类型", example = "PRODUCT_CHANNEL")
    private String resourceType;

    @Schema(description = "目标部门ID（运营部门ID）", example = "20")
    private Long targetDeptId;

    @Schema(description = "产品ID", example = "1")
    private Long productId;

    @Schema(description = "渠道代码", example = "ANDROID")
    private String channelCode;

    @Schema(description = "运营队伍名称", example = "A组")
    private String operationTeam;

    @Schema(description = "区服名称", example = "测试服")
    private String serverName;

    @Schema(description = "状态：1-启用，0-禁用", example = "1")
    private Integer status;

    @Schema(description = "备注", example = "测试绑定")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    // 扩展字段
    @Schema(description = "运维部门名称", example = "运维一部")
    private String opsDeptName;

    @Schema(description = "目标部门名称", example = "运营一部")
    private String targetDeptName;

    @Schema(description = "产品名称", example = "测试游戏")
    private String productName;

    @Schema(description = "用户名称", example = "张三")
    private String userName;
} 