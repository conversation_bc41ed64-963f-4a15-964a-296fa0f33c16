package com.mandong.api.module.erp.dal.dataobject.department;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 部门表
 */
@TableName("qsdk_department")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap")
public class DepartmentDO {
    
    /**
     * 部门ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 父部门ID，0表示根部门
     */
    @TableField("parent_id")
    private Long parentId;
    
    /**
     * 部门名称
     */
    @TableField("dept_name")
    private String deptName;
    
    /**
     * 部门编码
     */
    @TableField("dept_code")
    private String deptCode;
    
    /**
     * 部门层级：1-主管部门，2-组长部门，3-组员部门
     */
    @TableField("dept_level")
    private Integer deptLevel;
    
    /**
     * 排序
     */
    @TableField("sort_order")
    private Integer sortOrder;
    
    /**
     * 状态：1-启用，0-禁用
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 部门描述
     */
    @TableField("description")
    private String description;
    
    /**
     * 部门业务类型：OPERATION-运营部门，OPS-运维部门
     */
    @TableField("dept_type")
    private String deptType;
    
    /**
     * 创建者
     */
    @TableField("creator")
    private String creator;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新者
     */
    @TableField("updater")
    private String updater;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 是否删除：1-删除，0-未删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;
} 