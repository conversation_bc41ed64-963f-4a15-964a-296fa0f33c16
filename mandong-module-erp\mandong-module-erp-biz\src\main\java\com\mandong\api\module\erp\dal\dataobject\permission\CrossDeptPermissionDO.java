package com.mandong.api.module.erp.dal.dataobject.permission;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mandong.api.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 跨部门权限表
 * 用于管理运维人员对运营部门的权限
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("qsdk_cross_dept_permission")
public class CrossDeptPermissionDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 源用户ID（运维人员）
     */
    @TableField("source_user_id")
    private Long sourceUserId;

    /**
     * 源部门ID（运维部门）
     */
    @TableField("source_dept_id")
    private Long sourceDeptId;

    /**
     * 目标部门ID（运营部门）
     */
    @TableField("target_dept_id")
    private Long targetDeptId;

    /**
     * 权限类型：LEAD_GROUP_MANAGE（带队管理）、OPT_GROUP_MANAGE（运维组管理）
     */
    @TableField("permission_type")
    private String permissionType;

    /**
     * 权限范围：ALL（全部）、SPECIFIC（指定范围）
     */
    @TableField("permission_scope")
    private String permissionScope;

    /**
     * 具体权限资源ID（如果是SPECIFIC范围）
     */
    @TableField("resource_ids")
    private String resourceIds;

    /**
     * 状态：1启用，0停用
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 逻辑删除：1已删除，0未删除
     */
    @TableField("deleted")
    private Boolean deleted;
} 