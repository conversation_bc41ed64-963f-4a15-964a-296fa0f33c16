package com.mandong.api.module.erp.dal.dataobject.permission;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 用户权限映射表
 */
@TableName("qsdk_user_permission_mapping")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = "transMap")
public class UserPermissionMappingDO {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 资源类型：PRODUCT-产品, CHANNEL-渠道, SERVER-区服
     */
    @TableField("resource_type")
    private String resourceType;
    
    /**
     * 资源ID
     */
    @TableField("resource_id")
    private String resourceId;
    
    /**
     * 权限类型：READ-读权限, WRITE-写权限, ALL-全部权限
     */
    @TableField("permission_type")
    private String permissionType;
    
    /**
     * 状态：1-启用，0-禁用
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 创建者
     */
    @TableField("creator")
    private String creator;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新者
     */
    @TableField("updater")
    private String updater;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 是否删除：1-删除，0-未删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;
} 