package com.mandong.api.module.erp.dal.dataobject.resourcebinding;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 用户资源绑定表
 */
@TableName("qsdk_user_resource_binding")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserResourceBindingDO {
    
    /**
     * 绑定ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 运维部门ID
     */
    @TableField("ops_dept_id")
    private Long opsDeptId;
    
    /**
     * 资源类型：PRODUCT_CHANNEL-产品渠道，OPERATION_TEAM-运营队伍，SERVER-区服
     */
    @TableField("resource_type")
    private String resourceType;
    
    /**
     * 目标部门ID（运营部门ID）
     */
    @TableField("target_dept_id")
    private Long targetDeptId;
    
    /**
     * 产品ID
     */
    @TableField("product_id")
    private Long productId;
    
    /**
     * 渠道代码
     */
    @TableField("channel_code")
    private String channelCode;
    
    /**
     * 运营队伍名称
     */
    @TableField("operation_team")
    private String operationTeam;
    
    /**
     * 区服名称
     */
    @TableField("server_name")
    private String serverName;
    
    /**
     * 状态：1-启用，0-禁用
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
    
    /**
     * 创建者
     */
    @TableField("creator")
    private String creator;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新者
     */
    @TableField("updater")
    private String updater;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 是否删除：1-删除，0-未删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;
} 