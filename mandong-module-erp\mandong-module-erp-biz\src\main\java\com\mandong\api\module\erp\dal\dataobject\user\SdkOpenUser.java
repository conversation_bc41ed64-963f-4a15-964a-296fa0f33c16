package com.mandong.api.module.erp.dal.dataobject.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "qsdk_open_user")
public class SdkOpenUser {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "manageUid")
    private Integer manageuid;

    @TableField(value = "`uid`")
    private Integer uid;

    /**
     * 用户第三方账号信息
     */
    @TableField(value = "userOpenId")
    private String useropenid;

    /**
     * 第三方类型
     */
    @TableField(value = "openType")
    private Boolean opentype;

    /**
     * 腾讯开放平台用户统一标识
     */
    @TableField(value = "tencentUnionid")
    private String tencentunionid;

    /**
     * 第三方用户名
     */
    @TableField(value = "otherAccountName")
    private String otheraccountname;

    @TableField(value = "otherInfo")
    private String otherinfo;
}