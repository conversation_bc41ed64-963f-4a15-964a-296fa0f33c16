package com.mandong.api.module.erp.dal.mysql.resourcebinding;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mandong.api.module.erp.dal.dataobject.resourcebinding.UserResourceBindingDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户资源绑定 Mapper
 */
@Mapper
public interface UserResourceBindingMapper extends BaseMapper<UserResourceBindingDO> {

    /**
     * 根据用户ID查询资源绑定
     */
    List<UserResourceBindingDO> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和资源类型查询绑定
     */
    List<UserResourceBindingDO> selectByUserIdAndResourceType(@Param("userId") Long userId, 
                                                             @Param("resourceType") String resourceType);

    /**
     * 根据部门ID查询资源绑定
     */
    List<UserResourceBindingDO> selectByDeptId(@Param("deptId") Long deptId);

    /**
     * 批量删除用户资源绑定
     */
    int deleteByUserId(@Param("userId") Long userId);

    /**
     * 获取用户可访问的产品ID列表
     */
    List<Long> selectAccessibleProductIds(@Param("userId") Long userId);

    /**
     * 获取用户可访问的渠道代码列表
     */
    List<String> selectAccessibleChannelCodes(@Param("userId") Long userId);

    /**
     * 获取用户可访问的运营队伍列表
     */
    List<String> selectAccessibleOperationTeams(@Param("userId") Long userId);

    /**
     * 获取用户可访问的区服列表
     */
    List<String> selectAccessibleServerNames(@Param("userId") Long userId);

    /**
     * 获取用户的完整权限绑定组合（用于生成精确权限SQL）
     */
    List<UserResourceBindingDO> selectUserPermissionCombinations(@Param("userId") Long userId);
} 