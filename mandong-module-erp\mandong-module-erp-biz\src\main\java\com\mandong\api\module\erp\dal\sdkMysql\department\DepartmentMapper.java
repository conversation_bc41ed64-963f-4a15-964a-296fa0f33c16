package com.mandong.api.module.erp.dal.sdkMysql.department;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.module.erp.dal.dataobject.department.DepartmentDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 部门Mapper
 */
@Mapper
@DS("sdkDB")
public interface DepartmentMapper extends BaseMapperX<DepartmentDO> {
} 