package com.mandong.api.module.erp.dal.sdkMysql.department;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.module.erp.dal.dataobject.department.UserDeptRelationDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户部门关系Mapper
 */
@Mapper
@DS("sdkDB")
public interface UserDeptRelationMapper extends BaseMapperX<UserDeptRelationDO> {
} 