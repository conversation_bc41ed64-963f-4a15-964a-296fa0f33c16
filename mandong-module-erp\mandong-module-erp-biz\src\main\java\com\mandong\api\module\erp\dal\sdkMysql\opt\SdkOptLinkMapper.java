package com.mandong.api.module.erp.dal.sdkMysql.opt;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.leadGroup.vo.LeadGroupLinksPageReqVO;
import com.mandong.api.module.erp.controller.admin.leadGroup.vo.LeadGroupLinksPageRespVO;
import com.mandong.api.module.erp.controller.admin.leadGroup.vo.LeadGroupMonthlyStatsVO;
import com.mandong.api.module.erp.controller.admin.opt.vo.OptLinksPageReqVO;
import com.mandong.api.module.erp.controller.admin.opt.vo.OptLinksPageRespVO;
import com.mandong.api.module.erp.controller.admin.opt.vo.OptMonthlyStatsVO;
import com.mandong.api.module.erp.controller.admin.opt.vo.OptVO;
import com.mandong.api.module.erp.controller.admin.report.vo.OptSalePriceVO;
import com.mandong.api.module.erp.dal.dataobject.leadGroup.SdkLeadGroupLinkDO;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptDO;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptLinkDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("sdkDB")
public interface SdkOptLinkMapper extends BaseMapperX<SdkOptLinkDO> {

    default PageResult<OptLinksPageRespVO> selectPage(OptLinksPageReqVO pageReqVO) {
        return selectJoinPage(pageReqVO, OptLinksPageRespVO.class, new MPJLambdaWrapperX<SdkOptLinkDO>()
                .eqIfPresent(SdkOptLinkDO::getLinkId, pageReqVO.getLinkId())
                .inIfPresent(SdkOptLinkDO::getProductId, pageReqVO.getProductId())
                .eqIfPresent(SdkOptLinkDO::getChannelCode, pageReqVO.getChannelCode())
                .betweenIfPresent(SdkOptLinkDO::getCreateTime,pageReqVO.getCreateTime())
                .orderByDesc(SdkOptLinkDO::getCreateTime)
        );
    }


    List<OptMonthlyStatsVO> getOptMonthlyStats(@Param("id") Long id, @Param("startTime") Long startTime, @Param("endTime")Long endTime) ;


    List<OptSalePriceVO> getReportOptSalePrice( @Param("startTime") Long startTime, @Param("endTime")Long endTime) ;


}