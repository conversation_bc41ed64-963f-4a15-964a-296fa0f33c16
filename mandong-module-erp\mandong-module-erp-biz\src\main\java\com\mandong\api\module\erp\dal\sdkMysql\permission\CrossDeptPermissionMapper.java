package com.mandong.api.module.erp.dal.sdkMysql.permission;

import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.module.erp.dal.dataobject.permission.CrossDeptPermissionDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 跨部门权限 Mapper
 */
@Mapper
public interface CrossDeptPermissionMapper extends BaseMapperX<CrossDeptPermissionDO> {
    
    /**
     * 查询用户的跨部门权限列表
     */
    default List<CrossDeptPermissionDO> selectListByUserId(Long userId) {
        return selectList(CrossDeptPermissionDO::getSourceUserId, userId);
    }
    
    /**
     * 查询用户对特定部门的权限列表
     */
    default List<CrossDeptPermissionDO> selectListByUserIdAndTargetDept(Long userId, Long targetDeptId) {
        return selectList(CrossDeptPermissionDO::getSourceUserId, userId,
                         CrossDeptPermissionDO::getTargetDeptId, targetDeptId);
    }
    
    /**
     * 查询用户的特定权限类型
     */
    default List<CrossDeptPermissionDO> selectListByUserIdAndType(Long userId, String permissionType) {
        return selectList(CrossDeptPermissionDO::getSourceUserId, userId,
                         CrossDeptPermissionDO::getPermissionType, permissionType);
    }
} 