package com.mandong.api.module.erp.dal.sdkMysql.role;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryMonthRespVO;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryReqVO;
import com.mandong.api.module.erp.controller.admin.role.vo.RolePageReqVO;
import com.mandong.api.module.erp.controller.admin.role.vo.RolePageRespVO;
import com.mandong.api.module.erp.controller.admin.role.vo.SdkRoleCondition;
import com.mandong.api.module.erp.controller.admin.server.vo.ServerPageRespVO;
import com.mandong.api.module.erp.controller.admin.user.vo.*;
import com.mandong.api.module.erp.dal.dataobject.channel.SdkChannelDO;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptLinkDO;
import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import com.mandong.api.module.erp.dal.dataobject.role.SdkRoleDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkUserDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkUserDevicesDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;

/**
 * 订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("sdkDB")
public interface SdkRoleMapper extends BaseMapperX<SdkRoleDO> {

    default PageResult<RolePageRespVO> selectRolePage(RolePageReqVO pageReqVO) {
        return selectJoinPage(pageReqVO, RolePageRespVO.class, new MPJLambdaWrapperX<SdkRoleDO>()
                .selectAll(SdkRoleDO.class)
                .eqIfPresent(SdkRoleDO::getUid, pageReqVO.getUid())
                .eqIfPresent(SdkRoleDO::getUserName, pageReqVO.getUsername())
                .eqIfPresent(SdkRoleDO::getGameRoleId, pageReqVO.getRoleId())
                .eqIfPresent(SdkRoleDO::getGameRoleName, pageReqVO.getRoleName())
                .eqIfPresent(SdkRoleDO::getServerName, pageReqVO.getServerName())
                .betweenIfPresent(SdkRoleDO::getLastLoginTime,pageReqVO.getLastLoginTime())
                .inIfPresent(SdkRoleDO::getChannelCode,pageReqVO.getChannelCode() )
                .inIfPresent(SdkRoleDO::getProductId,pageReqVO.getProductId())
                .leftJoin(SdkUserDO.class,SdkUserDO::getUid,SdkRoleDO::getUid)
                .select(SdkChannelDO::getChannelName)
                .leftJoin(SdkChannelDO.class,"channel", on-> on.eq(SdkChannelDO::getProductId,SdkUserDO::getProductId).eq(SdkChannelDO::getChannelCode, SdkUserDO::getChannelCode))
                .select(SdkProductDO::getProductName)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkRoleDO::getProductId)
                .orderByDesc(SdkRoleDO::getLastLoginTime)
        );
    }

    /**
     * 支持权限SQL的角色分页查询方法
     */
    default PageResult<RolePageRespVO> selectRolePageWithPermission(RolePageReqVO pageReqVO, String permissionSql) {
        MPJLambdaWrapperX<SdkRoleDO> queryWrapper = new MPJLambdaWrapperX<>();
        queryWrapper.selectAll(SdkRoleDO.class)
                .eqIfPresent(SdkRoleDO::getUid, pageReqVO.getUid())
                .eqIfPresent(SdkRoleDO::getUserName, pageReqVO.getUsername())
                .eqIfPresent(SdkRoleDO::getGameRoleId, pageReqVO.getRoleId())
                .inIfPresent(SdkRoleDO::getProductId,pageReqVO.getProductId())
                .inIfPresent(SdkRoleDO::getChannelCode,pageReqVO.getChannelCode())
                .eqIfPresent(SdkRoleDO::getGameRoleName, pageReqVO.getRoleName())
                .eqIfPresent(SdkRoleDO::getServerName, pageReqVO.getServerName())
                .betweenIfPresent(SdkRoleDO::getLastLoginTime, pageReqVO.getLastLoginTime())
                .leftJoin(SdkUserDO.class, SdkUserDO::getUid, SdkRoleDO::getUid)
                .select(SdkChannelDO::getChannelName)
                .leftJoin(SdkChannelDO.class,"channel", on-> on.eq(SdkChannelDO::getProductId,SdkUserDO::getProductId).eq(SdkChannelDO::getChannelCode, SdkUserDO::getChannelCode))
                .select(SdkProductDO::getProductName)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkRoleDO::getProductId)
                .orderByDesc(SdkRoleDO::getLastLoginTime);

        // 添加权限SQL条件
        if (permissionSql != null && !permissionSql.trim().isEmpty()) {
            queryWrapper.apply(permissionSql);
        } else {
            // 没有权限SQL时，使用原有的筛选逻辑
            if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
                queryWrapper.in(SdkRoleDO::getProductId, pageReqVO.getProductId());
            }
            if (pageReqVO.getChannelCode() != null && !pageReqVO.getChannelCode().isEmpty()) {
                queryWrapper.in(SdkRoleDO::getChannelCode, pageReqVO.getChannelCode());
            }
        }

        return selectJoinPage(pageReqVO, RolePageRespVO.class, queryWrapper);
    }

    IPage<RolePageRespVO> selectRolePageNew(IPage<RolePageRespVO> page, @Param("reqVO") RolePageReqVO pageReqVO, @Param("conditions")List<SdkRoleCondition> conditions);

    default SdkUserLiveSummaryRespVO getUserLiveSummaryByDay(SdkUserSummaryReqVO pageReqVO,List<SdkOrderCondition> conditions) {




        MPJLambdaWrapperX<SdkRoleDO> baseWrapper = (MPJLambdaWrapperX<SdkRoleDO>) new MPJLambdaWrapperX<SdkRoleDO>()
                .selectCount(SdkRoleDO::getUid,SdkUserLiveSummaryRespVO::getTodayLive)
                .betweenIfPresent(SdkRoleDO::getLastLoginTime,pageReqVO.getPayTime())
                .leftJoin(SdkRoleDO.class,SdkRoleDO::getUid,SdkUserDO::getUid)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkUserDO::getProductId)
                .groupBy(SdkRoleDO::getUid);



        // 如果没有条件列表，则使用请求中的产品ID
        // 添加产品ID和区服组合条件，确保它们之间的联系是正确的
        if (CollectionUtils.isNotEmpty(conditions)) {
            baseWrapper.and(wrapper -> {
                for (int i = 0; i < conditions.size(); i++) {
                    SdkOrderCondition condition = conditions.get(i);
                    if (i == 0) {
                        wrapper.and(w -> w.eq(SdkRoleDO::getProductId, condition.getProductId()).eq(SdkRoleDO::getServerName, condition.getServerName()));
                    } else {
                        wrapper.or(w -> w.eq(SdkRoleDO::getProductId, condition.getProductId()).eq(SdkRoleDO::getServerName, condition.getServerName()));
                    }
                }
            });
        } else {
            // 当没有条件时，继续使用旧的逻辑
            if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
                baseWrapper.inIfExists(SdkProductDO::getId, pageReqVO.getProductId());
            }
        }

        return selectJoinOne(SdkUserLiveSummaryRespVO.class, baseWrapper);
    }

    default SdkUserLiveSummaryRespVO getUserLiveSummaryByWeek(SdkUserSummaryReqVO pageReqVO,List<SdkOrderCondition> conditions) {
        // 如果前端传入了时间范围，则计算该时间所在周的开始和结束时间
        Long[] payTime = pageReqVO.getPayTime();
        Calendar calendar = Calendar.getInstance();

        if (payTime != null && payTime.length == 2) {
            calendar.setTimeInMillis(payTime[0] * 1000L);  // 转换为毫秒
        }

        // 获取当前日期所在周的周一
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        int offset = dayOfWeek == Calendar.SUNDAY ? -6 : (Calendar.MONDAY - dayOfWeek);
        calendar.add(Calendar.DAY_OF_MONTH, offset);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Long startTime = calendar.getTimeInMillis() / 1000;

        // 计算周日的日期
        calendar.add(Calendar.DAY_OF_MONTH, 6);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Long endTime = calendar.getTimeInMillis() / 1000;

        MPJLambdaWrapperX<SdkRoleDO> baseWrapper = (MPJLambdaWrapperX<SdkRoleDO>) new MPJLambdaWrapperX<SdkRoleDO>()
                .selectCount(SdkRoleDO::getUid,SdkUserLiveSummaryRespVO::getTodayLive)
                .betweenIfPresent(SdkRoleDO::getLastLoginTime,startTime,endTime)

                .leftJoin(SdkRoleDO.class,SdkRoleDO::getUid,SdkUserDO::getUid)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkUserDO::getProductId)
                .groupBy(SdkRoleDO::getUid);

        // 如果没有条件列表，则使用请求中的产品ID
        // 添加产品ID和区服组合条件，确保它们之间的联系是正确的
        if (CollectionUtils.isNotEmpty(conditions)) {
            baseWrapper.and(wrapper -> {
                for (int i = 0; i < conditions.size(); i++) {
                    SdkOrderCondition condition = conditions.get(i);
                    if (i == 0) {
                        wrapper.and(w -> w.eq(SdkRoleDO::getProductId, condition.getProductId()).eq(SdkRoleDO::getServerName, condition.getServerName()));
                    } else {
                        wrapper.or(w -> w.eq(SdkRoleDO::getProductId, condition.getProductId()).eq(SdkRoleDO::getServerName, condition.getServerName()));
                    }
                }
            });
        } else {
            // 当没有条件时，继续使用旧的逻辑
            if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
                baseWrapper.inIfExists(SdkProductDO::getId, pageReqVO.getProductId());
            }
        }


        return selectJoinOne(SdkUserLiveSummaryRespVO.class, baseWrapper);
    }

    default List<SdkOrderSummaryMonthRespVO> getUserLiveSummaryByMonth(SdkUserSummaryReqVO pageReqVO, List<SdkOrderCondition> conditions) {
        // 默认初始化为当前月份
        Long startTime;
        Long endTime;

        Long[] payTime = pageReqVO.getPayTime();
        if (payTime != null && payTime.length == 2) {
            // 计算传入时间的月份开始和结束
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(payTime[0] * 1000L);  // 转换为毫秒
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startTime = calendar.getTimeInMillis() / 1000;

            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.SECOND, -1);
            endTime = calendar.getTimeInMillis() / 1000;
        } else {
            // 如果未指定时间，使用当前月
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startTime = calendar.getTimeInMillis() / 1000;

            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.SECOND, -1);
            endTime = calendar.getTimeInMillis() / 1000;
        }

        // 从条件中提取产品ID和服务器名称
        List<Long> productIds = null;
        List<String> serverNames = null;

        if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
            productIds = pageReqVO.getProductId();
        }

        return getDailyLive( startTime,endTime,productIds,serverNames,conditions);
    }


    @Select("""
            <script>
            SELECT 
                days.day as time,
                IFNULL(t.price, 0) as price
            FROM (
                SELECT 1 as day UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION
                SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION
                SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
                SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION SELECT 20 UNION
                SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION SELECT 25 UNION
                SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION SELECT 30 UNION
                SELECT 31
            ) days
            LEFT JOIN (
                SELECT FROM_UNIXTIME(payTime, '%Y-%m-%d') as full_date,
                       FROM_UNIXTIME(payTime, '%d') as day,
                       COUNT(o.uid) as price
                FROM qsdk_roles o
                LEFT JOIN qsdk_product p ON p.id = o.productId
                WHERE  
                      o.lastLoginTime BETWEEN #{startTime} AND #{endTime}
                    <choose>
                        <when test="conditions != null and conditions.size() > 0">
                            AND (
                                <foreach collection="conditions" item="condition" separator=" OR ">
                                    (o.productId = #{condition.productId} AND o.serverName = #{condition.serverName})
                                </foreach>
                            )
                        </when>
                        <otherwise>
                            <if test="productId != null and productId.size() > 0">
                            AND p.id IN 
                            <foreach collection="productId" item="id" open="(" separator="," close=")">
                                #{id}
                            </foreach>
                            </if>
                            <if test="serverNames != null and serverNames.size() > 0">
                            AND o.serverName IN 
                            <foreach collection="serverNames" item="serverName" open="(" separator="," close=")">
                                #{serverName}
                            </foreach>
                            </if>
                        </otherwise>
                    </choose>
                GROUP BY full_date, day
            ) t ON days.day = t.day
            WHERE days.day BETWEEN DAY(FROM_UNIXTIME(#{startTime})) AND DAY(FROM_UNIXTIME(#{endTime}))
                AND MONTH(FROM_UNIXTIME(#{startTime})) = MONTH(FROM_UNIXTIME(#{endTime}))
            ORDER BY days.day ASC
            </script>
            """)
    List<SdkOrderSummaryMonthRespVO> getDailyLive(@Param("startTime") Long startTime,
                                                         @Param("endTime") Long endTime,
                                                         @Param("productId") List<Long> productId,
                                                         @Param("serverNames") List<String> serverNames,
                                                         @Param("conditions") List<SdkOrderCondition> conditions);

}