package com.mandong.api.module.erp.dal.sdkMysql.user;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.module.erp.dal.dataobject.user.SdkOpenUser;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("sdkDB")
public interface SdkOpenUserMapper extends BaseMapperX<SdkOpenUser> {
}