package com.mandong.api.module.erp.dal.sdkMysql.user;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.mapper.BaseMapperX;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.module.erp.controller.admin.order.vo.*;
import com.mandong.api.module.erp.controller.admin.user.vo.*;
import com.mandong.api.module.erp.dal.dataobject.channel.SdkChannelDO;
import com.mandong.api.module.erp.dal.dataobject.config.SdkPaysDO;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptDO;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptLinkDO;
import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import com.mandong.api.module.erp.dal.dataobject.role.SdkRoleDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkLogsUserLoginDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkUserDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkUserDevicesDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import java.util.ArrayList;

/**
 * 订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("sdkDB")
public interface SdkUserMapper extends BaseMapperX<SdkUserDO> {


    default PageResult<UserPageRespVO> selectPage(UserPageReqVO pageReqVO) {
        return selectJoinPage(pageReqVO, UserPageRespVO.class, new MPJLambdaWrapperX<SdkUserDO>()
                .selectAll(SdkUserDO.class)
                .eqIfPresent(SdkUserDO::getUsername, pageReqVO.getUsername())
                .eqIfPresent(SdkUserDO::getUid, pageReqVO.getUid())
                .betweenIfPresent(SdkUserDO::getRegTime,pageReqVO.getRegTime())
                .inIfPresent(SdkUserDO::getProductId, pageReqVO.getProductId())
                .inIfExists(SdkChannelDO::getChannelCode,pageReqVO.getChannelCode())
                .select(SdkChannelDO::getChannelName)
                .leftJoin(SdkChannelDO.class,"channel", on-> on.eq(SdkChannelDO::getProductId,SdkUserDO::getProductId).eq(SdkChannelDO::getChannelCode, SdkUserDO::getChannelCode))
                .select(SdkProductDO::getProductName)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkUserDO::getProductId)
                .selectSum(SdkOrderDO::getDealAmount, UserPageRespVO::getTotalAmount)
                .leftJoin(SdkOrderDO.class,"orders", on-> on.eq(SdkOrderDO::getUid, SdkUserDO::getUid))
                .select("IF(o.user_name is null,channel.channelName,o.user_name) as optName")
                .leftJoin(SdkOptLinkDO.class, o->o.eq(SdkUserDO::getProductId,SdkOptLinkDO::getProductId).eq(SdkUserDO::getChannelCode,SdkOptLinkDO::getChannelCode))
                .leftJoin(SdkOptDO.class,"o",SdkOptDO::getId,SdkOptLinkDO::getLinkId)
                .groupBy(SdkUserDO::getUid)
                .orderByDesc(SdkUserDO::getRegTime)
        );
    }

    /**
     * 支持权限SQL的用户分页查询方法
     */
    default PageResult<UserPageRespVO> selectPageWithPermission(UserPageReqVO pageReqVO, String permissionSql) {
        MPJLambdaWrapperX<SdkUserDO> queryWrapper = new MPJLambdaWrapperX<>();
        queryWrapper.selectAll(SdkUserDO.class)
                .eqIfPresent(SdkUserDO::getUsername, pageReqVO.getUsername())
                .eqIfPresent(SdkUserDO::getUid, pageReqVO.getUid())
                .inIfPresent(SdkUserDO::getProductId, pageReqVO.getProductId())
                .inIfExists(SdkChannelDO::getChannelCode,pageReqVO.getChannelCode())
                .betweenIfPresent(SdkUserDO::getRegTime, pageReqVO.getRegTime())
                .select(SdkChannelDO::getChannelName)
                .leftJoin(SdkChannelDO.class,"channel", on-> on.eq(SdkChannelDO::getProductId,SdkUserDO::getProductId).eq(SdkChannelDO::getChannelCode, SdkUserDO::getChannelCode))
                .select(SdkProductDO::getProductName)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkUserDO::getProductId)

                .selectSum(SdkOrderDO::getDealAmount, UserPageRespVO::getTotalAmount)
                .leftJoin(SdkOrderDO.class,"orders", on-> on.eq(SdkOrderDO::getUid, SdkUserDO::getUid))
                .select("IF(o.user_name is null,channel.channelName,o.user_name) as optName")
                .leftJoin(SdkOptLinkDO.class, o->o.eq(SdkUserDO::getProductId,SdkOptLinkDO::getProductId).eq(SdkUserDO::getChannelCode,SdkOptLinkDO::getChannelCode))
                .leftJoin(SdkOptDO.class,"o",SdkOptDO::getId,SdkOptLinkDO::getLinkId)
                .groupBy(SdkUserDO::getUid)
                .orderByDesc(SdkUserDO::getRegTime);

        // 添加权限SQL条件
        if (permissionSql != null && !permissionSql.trim().isEmpty()) {
            queryWrapper.apply(permissionSql);
        } else {
            // 没有权限SQL时，使用原有的筛选逻辑
            if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
                queryWrapper.in(SdkUserDO::getProductId, pageReqVO.getProductId());
            }
            if (pageReqVO.getChannelCode() != null && !pageReqVO.getChannelCode().isEmpty()) {
                queryWrapper.in(SdkUserDO::getChannelCode, pageReqVO.getChannelCode());
            }
        }

        return selectJoinPage(pageReqVO, UserPageRespVO.class, queryWrapper);
    }



    default UserDetailRespVO getUserDetail(Long id) {
        return selectJoinOne(UserDetailRespVO.class, new MPJLambdaWrapperX<SdkUserDO>()
                .selectAll(SdkUserDO.class)
                .eqIfPresent(SdkUserDO::getUid, id)
                .select(SdkChannelDO::getChannelName)
                .leftJoin(SdkChannelDO.class,"channel", on-> on.eq(SdkChannelDO::getProductId,SdkUserDO::getProductId).eq(SdkChannelDO::getChannelCode, SdkUserDO::getChannelCode))
                .select(SdkProductDO::getProductName)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkUserDO::getProductId)
                .selectSum(SdkOrderDO::getDealAmount, UserPageRespVO::getTotalAmount)
                .leftJoin(SdkOrderDO.class,"orders", on-> on.eq(SdkOrderDO::getUid, SdkUserDO::getUid))

                .select("(SELECT deviceId FROM qsdk_user_devices WHERE uid = t.uid ORDER BY activeTime DESC LIMIT 1) AS deviceId")
                .groupBy(SdkUserDO::getUid)


        );
    }

    default SdkUserSummaryRespVO getUserRegistrationSummaryByDay(SdkOrderSummaryReqVO pageReqVO) {
        MPJLambdaWrapperX<SdkUserDO> baseWrapper = new MPJLambdaWrapperX<SdkUserDO>();
        baseWrapper.selectCount(SdkUserDO::getRegTime,SdkUserSummaryRespVO::getTodayRegistration)
                .betweenIfPresent(SdkUserDO::getRegTime,pageReqVO.getPayTime())
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkUserDO::getProductId);
                
        // 添加游戏名称条件
        if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
            baseWrapper.inIfExists(SdkProductDO::getId, pageReqVO.getProductId());
        }
                
        return selectJoinOne(SdkUserSummaryRespVO.class, baseWrapper);
    }
    
    /**
     * 支持权限SQL的重载方法
     */
    default SdkUserSummaryRespVO getUserRegistrationSummaryByDay(SdkOrderSummaryReqVO pageReqVO, String permissionSql) {
        MPJLambdaWrapperX<SdkUserDO> baseWrapper = new MPJLambdaWrapperX<SdkUserDO>();
        baseWrapper.selectCount(SdkUserDO::getRegTime,SdkUserSummaryRespVO::getTodayRegistration)
                .betweenIfPresent(SdkUserDO::getRegTime,pageReqVO.getPayTime())
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkUserDO::getProductId);
                
        // 添加权限SQL条件
        if (permissionSql != null && !permissionSql.trim().isEmpty()) {
            baseWrapper.apply(permissionSql);
        } else {
            // 添加游戏名称条件
            if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
                baseWrapper.inIfExists(SdkProductDO::getId, pageReqVO.getProductId());
            }
        }
                
        return selectJoinOne(SdkUserSummaryRespVO.class, baseWrapper);
    }



    default SdkUserSummaryRespVO getUserRegistrationByDay(SdkOrderSummaryReqVO pageReqVO) {
        MPJLambdaWrapperX<SdkUserDO> baseWrapper = new MPJLambdaWrapperX<SdkUserDO>();
        baseWrapper.betweenIfPresent(SdkUserDO::getRegTime,pageReqVO.getPayTime())
                .inIfPresent(SdkUserDO::getChannelCode,pageReqVO.getChannelCode())
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkUserDO::getProductId)
                .leftJoin(SdkRoleDO.class,SdkRoleDO::getUid,SdkUserDO::getUid)
                .eqIfExists(SdkRoleDO::getServerName,pageReqVO.getServerName())
                .select(" Count( DISTINCT t.uid) as todayRegistration");


        // 添加游戏名称条件
        if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
            baseWrapper.inIfExists(SdkProductDO::getId, pageReqVO.getProductId());
        }

        return selectJoinOne(SdkUserSummaryRespVO.class, baseWrapper);
    }



    default SdkUserSummaryRespVO getUserRegistrationSummaryByWeek(SdkOrderSummaryReqVO pageReqVO) {
        // 如果前端传入了时间范围，则计算该时间所在周的开始和结束时间
        Long[] payTime = pageReqVO.getPayTime();
        Calendar calendar = Calendar.getInstance();

        if (payTime != null && payTime.length == 2) {
            calendar.setTimeInMillis(payTime[0] * 1000L);  // 转换为毫秒
        }

        // 获取当前日期所在周的周一
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        int offset = dayOfWeek == Calendar.SUNDAY ? -6 : (Calendar.MONDAY - dayOfWeek);
        calendar.add(Calendar.DAY_OF_MONTH, offset);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Long startTime = calendar.getTimeInMillis() / 1000;

        // 计算周日的日期
        calendar.add(Calendar.DAY_OF_MONTH, 6);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Long endTime = calendar.getTimeInMillis() / 1000;

        MPJLambdaWrapperX<SdkUserDO> baseWrapper = new MPJLambdaWrapperX<SdkUserDO>();
        baseWrapper.selectCount(SdkUserDO::getRegTime,SdkUserSummaryRespVO::getWeekRegistration)
                .between(SdkUserDO::getRegTime,startTime,endTime)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkUserDO::getProductId);
                
        // 添加游戏名称条件
        if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
            baseWrapper.inIfExists(SdkProductDO::getId, pageReqVO.getProductId());
        }
                
        return selectJoinOne(SdkUserSummaryRespVO.class, baseWrapper);
    }
    
    /**
     * 支持权限SQL的重载方法
     */
    default SdkUserSummaryRespVO getUserRegistrationSummaryByWeek(SdkOrderSummaryReqVO pageReqVO, String permissionSql) {
        // 如果前端传入了时间范围，则计算该时间所在周的开始和结束时间
        Long[] payTime = pageReqVO.getPayTime();
        Calendar calendar = Calendar.getInstance();

        if (payTime != null && payTime.length == 2) {
            calendar.setTimeInMillis(payTime[0] * 1000L);  // 转换为毫秒
        }

        // 获取当前日期所在周的周一
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        int offset = dayOfWeek == Calendar.SUNDAY ? -6 : (Calendar.MONDAY - dayOfWeek);
        calendar.add(Calendar.DAY_OF_MONTH, offset);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Long startTime = calendar.getTimeInMillis() / 1000;

        // 计算周日的日期
        calendar.add(Calendar.DAY_OF_MONTH, 6);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Long endTime = calendar.getTimeInMillis() / 1000;

        MPJLambdaWrapperX<SdkUserDO> baseWrapper = new MPJLambdaWrapperX<SdkUserDO>();
        baseWrapper.selectCount(SdkUserDO::getRegTime,SdkUserSummaryRespVO::getWeekRegistration)
                .between(SdkUserDO::getRegTime,startTime,endTime)
                .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkUserDO::getProductId);
                
        // 添加权限SQL条件
        if (permissionSql != null && !permissionSql.trim().isEmpty()) {
            baseWrapper.apply(permissionSql);
        } else {
            // 添加游戏名称条件
            if (pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty()) {
                baseWrapper.inIfExists(SdkProductDO::getId, pageReqVO.getProductId());
            }
        }
                
        return selectJoinOne(SdkUserSummaryRespVO.class, baseWrapper);
    }



    /**
     * 按天统计注册用户数
     */
    @Select("""
            <script>
            SELECT 
                days.day as time,
                IFNULL(t.price, 0) as price
            FROM (
                SELECT 1 as day UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION
                SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION
                SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
                SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION SELECT 20 UNION
                SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION SELECT 25 UNION
                SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION SELECT 30 UNION
                SELECT 31
            ) days
            LEFT JOIN (
                SELECT FROM_UNIXTIME(regTime, '%Y-%m-%d') as full_date,
                       FROM_UNIXTIME(regTime, '%d') as day,
                       COUNT(DISTINCT username) as price
                FROM qsdk_user o
                LEFT JOIN qsdk_product p ON p.id = o.productId
                WHERE o.regTime BETWEEN #{startTime} AND #{endTime}
                    <if test="conditions != null and conditions.size() > 0">
                    AND (
                        <foreach collection="conditions" item="condition" separator=" OR ">
                            (o.productId = #{condition.productId} AND o.serverName = #{condition.serverName}
                              <if test="condition.channelCode != null and condition.channelCode != ''">
                                                           and o.channelCode = #{condition.channelCode}
                                    </if>
                            )
                        </foreach>
                    )
                    </if>
                    <if test="conditions == null">
                        <if test="productId != null and productId.size() > 0">
                        AND p.id IN 
                        <foreach collection="productId" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                        </if>
                        <if test="serverNames != null and serverNames.size() > 0">
                        AND o.serverName IN 
                        <foreach collection="serverNames" item="serverName" open="(" separator="," close=")">
                            #{serverName}
                        </foreach>
                        </if>
                    </if>
                GROUP BY full_date, day
            ) t ON days.day = t.day
            WHERE days.day BETWEEN DAY(FROM_UNIXTIME(#{startTime})) AND DAY(FROM_UNIXTIME(#{endTime}))
                AND MONTH(FROM_UNIXTIME(#{startTime})) = MONTH(FROM_UNIXTIME(#{endTime}))
            ORDER BY days.day ASC
            </script>
            """)
    List<SdkUserSummaryMonthRespVO> getMonthlyRegisterUsers(
        @Param("startTime") Long startTime, 
        @Param("endTime") Long endTime, 
        @Param("productId") List<Long> productId,
        @Param("serverNames") List<String> serverNames,
        @Param("conditions") List<SdkOrderCondition> conditions);

    default List<SdkUserSummaryMonthRespVO> getMonthlyRegisterUsers(
            SdkUserSummaryReqVO reqVO,
            List<SdkOrderCondition> conditions) {
        // 默认初始化为当前月份
        Long startTime;
        Long endTime;
        
        Long[] payTime = reqVO.getPayTime();
        if (payTime != null && payTime.length == 2) {
            // 计算传入时间的月份开始和结束
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(payTime[0] * 1000L);  // 转换为毫秒
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startTime = calendar.getTimeInMillis() / 1000;

            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.SECOND, -1);
            endTime = calendar.getTimeInMillis() / 1000;
        } else {
            // 如果未指定时间，使用当前月
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startTime = calendar.getTimeInMillis() / 1000;
            
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.SECOND, -1);
            endTime = calendar.getTimeInMillis() / 1000;
        }
        
        // 从条件中提取产品ID和服务器名称
        List<Long> productIds = null;
        List<String> serverNames = null;
        
        if (reqVO.getProductId() != null && !reqVO.getProductId().isEmpty()) {
            productIds = reqVO.getProductId();
        }

        // 调用SQL查询方法
        return getMonthlyRegisterUsers(startTime, endTime, productIds, serverNames, conditions);
    }

    // 保留原方法，但内部调用新方法
    default List<SdkUserSummaryMonthRespVO> getMonthlyRegisterUsers(SdkUserSummaryReqVO reqVO) {
        return getMonthlyRegisterUsersWithConditions(reqVO, null);
    }
    
    /**
     * 支持权限SQL的重载方法
     */
    default List<SdkUserSummaryMonthRespVO> getMonthlyRegisterUsersWithSql(@Param("reqVO") SdkUserSummaryReqVO reqVO, @Param("permissionSql") String permissionSql) {
        // 默认初始化为当前月份
        Long startTime;
        Long endTime;
        
        Long[] payTime = reqVO.getPayTime();
        if (payTime != null && payTime.length == 2) {
            // 计算传入时间的月份开始和结束
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(payTime[0] * 1000L);  // 转换为毫秒
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startTime = calendar.getTimeInMillis() / 1000;

            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.SECOND, -1);
            endTime = calendar.getTimeInMillis() / 1000;
        } else {
            // 如果未指定时间，使用当前月
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startTime = calendar.getTimeInMillis() / 1000;
            
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.SECOND, -1);
            endTime = calendar.getTimeInMillis() / 1000;
        }
        
        // 从条件中提取产品ID和服务器名称
        List<Long> productIds = null;
        List<String> serverNames = null;
        
        if (reqVO.getProductId() != null && !reqVO.getProductId().isEmpty()) {
            productIds = reqVO.getProductId();
        }

        // 调用SQL查询方法
        return getMonthlyRegisterUsers(startTime, endTime, productIds, serverNames, null);
    }

    /**
     * 重命名原有的条件方法，避免重载歧义
     */
    default List<SdkUserSummaryMonthRespVO> getMonthlyRegisterUsersWithConditions(
            SdkUserSummaryReqVO reqVO,
            List<SdkOrderCondition> conditions) {
        // 默认初始化为当前月份
        Long startTime;
        Long endTime;
        
        Long[] payTime = reqVO.getPayTime();
        if (payTime != null && payTime.length == 2) {
            // 计算传入时间的月份开始和结束
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(payTime[0] * 1000L);  // 转换为毫秒
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startTime = calendar.getTimeInMillis() / 1000;

            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.SECOND, -1);
            endTime = calendar.getTimeInMillis() / 1000;
        } else {
            // 如果未指定时间，使用当前月
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startTime = calendar.getTimeInMillis() / 1000;
            
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.SECOND, -1);
            endTime = calendar.getTimeInMillis() / 1000;
        }
        
        // 从条件中提取产品ID和服务器名称
        List<Long> productIds = null;
        List<String> serverNames = null;
        
        if (reqVO.getProductId() != null && !reqVO.getProductId().isEmpty()) {
            productIds = reqVO.getProductId();
        }

        // 调用SQL查询方法
        return getMonthlyRegisterUsers(startTime, endTime, productIds, serverNames, conditions);
    }

    /**
     * 支持权限SQL的重载方法（改为default实现）
     */
    default List<SdkUserSummaryMonthRespVO> getMonthlyRegisterUsersWithPermission(@Param("reqVO") SdkUserSummaryReqVO reqVO, @Param("permissionSql") String permissionSql) {
        // 默认初始化为当前月份
        Long startTime;
        Long endTime;
        
        Long[] payTime = reqVO.getPayTime();
        if (payTime != null && payTime.length == 2) {
            // 计算传入时间的月份开始和结束
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(payTime[0] * 1000L);  // 转换为毫秒
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startTime = calendar.getTimeInMillis() / 1000;

            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.SECOND, -1);
            endTime = calendar.getTimeInMillis() / 1000;
        } else {
            // 如果未指定时间，使用当前月
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startTime = calendar.getTimeInMillis() / 1000;
            
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.SECOND, -1);
            endTime = calendar.getTimeInMillis() / 1000;
        }
        
        // 如果有权限SQL，使用用户表的直接查询
        if (permissionSql != null && !permissionSql.trim().isEmpty()) {
            MPJLambdaWrapperX<SdkUserDO> queryWrapper = new MPJLambdaWrapperX<SdkUserDO>();
            queryWrapper.select("FROM_UNIXTIME(t.regTime, '%d') AS time", 
                              "COUNT(DISTINCT t.username) AS price")
                       .between(SdkUserDO::getRegTime, startTime, endTime)
                       .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkUserDO::getProductId)
                       .apply(permissionSql)
                       .groupBy("FROM_UNIXTIME(t.regTime, '%Y-%m-%d')", "FROM_UNIXTIME(t.regTime, '%d')")
                       .orderBy(true, true, "FROM_UNIXTIME(t.regTime, '%d')");
            
            List<SdkUserSummaryMonthRespVO> directResult = selectJoinList(SdkUserSummaryMonthRespVO.class, queryWrapper);
            
            // 补全1-31天的数据，没有数据的天数设为0
            List<SdkUserSummaryMonthRespVO> result = new ArrayList<>();
            for (int day = 1; day <= 31; day++) {
                int finalDay = day;
                SdkUserSummaryMonthRespVO dayData = directResult.stream()
                    .filter(item -> Integer.parseInt(item.getTime()) == finalDay)
                    .findFirst()
                    .orElse(new SdkUserSummaryMonthRespVO());
                
                if (dayData.getTime() == null) {
                    dayData.setTime(String.valueOf(day));
                    dayData.setPrice(0F);
                }
                result.add(dayData);
            }
            
            return result;
        } else {
            // 没有权限SQL时，使用原有逻辑
            List<Long> productIds = null;
            if (reqVO.getProductId() != null && !reqVO.getProductId().isEmpty()) {
                productIds = reqVO.getProductId();
            }
            return getMonthlyRegisterUsers(startTime, endTime, productIds, null, null);
        }
    }

    /**
     * 获取指定时间范围内的日活跃用户数，支持按产品ID和服务器名称筛选
     * 
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @param productId 产品ID
     * @param serverName 服务器名称
     * @return 活跃用户数
     */
    @Select("""
            <script>
            SELECT COUNT(1) as activeUsers
            FROM (
                SELECT r.uid
                FROM qsdk_roles r
                LEFT JOIN qsdk_user_online t ON t.uid = CONVERT(r.uid, UNSIGNED)
                WHERE t.createTime BETWEEN #{startTime} AND #{endTime}
                AND r.productId = #{productId} 
                AND r.serverName = #{serverName}
                GROUP BY t.uid
            ) a
            </script>
            """)
    Long getDailyActiveUsers(
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("productId") String productId,
            @Param("serverName") String serverName);
    
    /**
     * 获取指定时间范围内的日活跃用户数，支持按条件列表筛选，避免交叉查询问题
     * 
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @param conditions 产品ID和服务器名称的组合条件
     * @return 活跃用户数
     */
    @Select("""
            <script>
            SELECT COUNT(1) as activeUsers
            FROM (
                SELECT r.uid
                FROM qsdk_roles r
                LEFT JOIN qsdk_user_online t ON t.uid = CONVERT(r.uid, UNSIGNED)
                WHERE t.createTime BETWEEN #{startTime} AND #{endTime}
                <if test="conditions != null and conditions.size() > 0">
                AND (
                    <foreach collection="conditions" item="condition" separator=" OR ">
                        (r.productId = #{condition.productId} AND r.serverName = #{condition.serverName}
                          <if test="condition.channelCode != null and condition.channelCode != ''">
                                                           and r.channelCode = #{condition.channelCode}
                                    </if>
                        )
                    </foreach>
                )
                </if>
                <if test="conditions == null">
                    <if test="productIds != null and productIds.size() > 0">
                    AND r.productId IN 
                    <foreach collection="productIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                    </if>
                    <if test="serverNames != null and serverNames.size() > 0">
                    AND r.serverName IN 
                    <foreach collection="serverNames" item="serverName" open="(" separator="," close=")">
                        #{serverName}
                    </foreach>
                    </if>
                </if>
                GROUP BY t.uid
            ) a
            </script>
            """)
    Long getDailyActiveUsersWithConditions(
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("productIds") List<Long> productIds,
            @Param("serverNames") List<String> serverNames,
            @Param("conditions") List<SdkOrderCondition> conditions);
            
    /**
     * 通过请求参数获取日活跃用户数，先检查权限再执行查询
     * 
     * @param reqVO 请求参数
     * @param permittedConditions 用户具有权限的产品ID和服务器名称组合条件
     * @return 日活跃用户数据
     */
    default SdkUserSummaryRespVO getDailyActiveUsersSummary(SdkUserSummaryReqVO reqVO, List<SdkOrderCondition> permittedConditions) {
        // 获取时间范围
        Long[] timeRange = reqVO.getPayTime();
        Long startTime, endTime;
        
        if (timeRange != null && timeRange.length == 2) {
            // 使用指定的时间范围
            startTime = timeRange[0];
            endTime = timeRange[1];
        } else {
            // 默认使用当天
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startTime = calendar.getTimeInMillis() / 1000;
            
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            endTime = calendar.getTimeInMillis() / 1000;
        }
        if (permittedConditions == null || permittedConditions.isEmpty()) {
            // 查询日活跃用户数
            Long activeUsers = getDailyActiveUsersWithConditions(startTime, endTime, reqVO.getProductId(), null, null);

            // 封装结果
            SdkUserSummaryRespVO result = new SdkUserSummaryRespVO();
            result.setTodayActiveUsers(activeUsers);
            return result;
        }


        // 检查是否有搜索条件
        List<SdkOrderCondition> effectiveConditions = null;
        
        // 先检查用户的请求参数中是否指定了查询条件
        if (reqVO.getProductId() != null && !reqVO.getProductId().isEmpty()) {
            
            // 有指定查询条件，检查是否在权限范围内
            List<SdkOrderCondition> requestConditions = new ArrayList<>();
            

            // 只有产品ID的情况，需要用权限条件中的服务器名称
            if (reqVO.getProductId() != null && !reqVO.getProductId().isEmpty()) {
                // 从权限条件中提取与请求产品ID匹配的条件
                for (SdkOrderCondition permittedCondition : permittedConditions) {
                    if (reqVO.getProductId().contains(permittedCondition.getProductId())) {
                        requestConditions.add(permittedCondition);
                    }
                }
            }
            
            // 检查请求条件是否在权限范围内，只保留有权限的条件
            if (!requestConditions.isEmpty() && permittedConditions != null && !permittedConditions.isEmpty()) {
                effectiveConditions = new ArrayList<>();
                for (SdkOrderCondition requestCondition : requestConditions) {
                    for (SdkOrderCondition permittedCondition : permittedConditions) {
                        if (requestCondition.getProductId().equals(permittedCondition.getProductId()) && 
                            requestCondition.getServerName().equals(permittedCondition.getServerName())) {
                            effectiveConditions.add(requestCondition);
                            break;
                        }
                    }
                }
            }
        } else {
            // 没有指定查询条件，使用权限条件
            effectiveConditions = permittedConditions;
        }
        
        // 如果没有有效条件（可能是权限不足或者条件为空），返回空结果
        if (effectiveConditions == null || effectiveConditions.isEmpty()) {
            SdkUserSummaryRespVO emptyResult = new SdkUserSummaryRespVO();
            emptyResult.setTodayActiveUsers(0L);
            return emptyResult;
        }
        
        // 查询日活跃用户数
        Long activeUsers = getDailyActiveUsersWithConditions(startTime, endTime, null, null, effectiveConditions);
        
        // 封装结果
        SdkUserSummaryRespVO result = new SdkUserSummaryRespVO();
        result.setTodayActiveUsers(activeUsers);
        return result;
    }
    
    /**
     * 不带条件的方法版本，内部会使用用户权限
     */
    default SdkUserSummaryRespVO getDailyActiveUsersSummary(SdkUserSummaryReqVO reqVO) {
        // 此方法应该由 Service 层调用并传入权限条件
        // 在没有权限条件的情况下返回空结果
        SdkUserSummaryRespVO emptyResult = new SdkUserSummaryRespVO();
        emptyResult.setTodayActiveUsers(0L);
        return emptyResult;
    }
    
    /**
     * 支持权限SQL的重载方法
     */
    default SdkUserSummaryRespVO getDailyActiveUsersSummary(@Param("reqVO") SdkUserSummaryReqVO reqVO, @Param("permissionSql") String permissionSql) {
        // 此方法应该由 Service 层调用并传入权限条件
        // 在没有权限条件的情况下返回空结果
        SdkUserSummaryRespVO emptyResult = new SdkUserSummaryRespVO();
        emptyResult.setTodayActiveUsers(0L);
        return emptyResult;
    }
    
    /**
     * 支持权限SQL的重载方法（改为default实现）
     */
    default SdkUserSummaryRespVO getDailyActiveUsersSummaryWithPermission(@Param("reqVO") SdkUserSummaryReqVO reqVO, @Param("permissionSql") String permissionSql) {
        // 获取时间范围
        Long[] timeRange = reqVO.getPayTime();
        Long startTime, endTime;
        
        if (timeRange != null && timeRange.length == 2) {
            // 使用指定的时间范围
            startTime = timeRange[0];
            endTime = timeRange[1];
        } else {
            // 默认使用当天
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startTime = calendar.getTimeInMillis() / 1000;
            
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            endTime = calendar.getTimeInMillis() / 1000;
        }
        
        // 如果有权限SQL，使用复杂的权限查询
        if (permissionSql != null && !permissionSql.trim().isEmpty()) {
            MPJLambdaWrapperX<SdkUserDO> queryWrapper = new MPJLambdaWrapperX<SdkUserDO>();
            queryWrapper.select("COUNT(DISTINCT t.uid) AS todayActiveUsers")
                       .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkUserDO::getProductId)
                       .leftJoin(SdkRoleDO.class, "r", on -> on.eq(SdkRoleDO::getUid, SdkUserDO::getUid))
                       .apply("EXISTS (SELECT 1 FROM qsdk_user_online online WHERE online.uid = t.uid AND online.createTime BETWEEN " + startTime + " AND " + endTime + ")")
                       .apply(permissionSql);
            
            return selectJoinOne(SdkUserSummaryRespVO.class, queryWrapper);
        } else {
            // 没有权限SQL时，使用原有逻辑
            Long activeUsers = getDailyActiveUsersWithConditions(startTime, endTime, reqVO.getProductId(), null, null);
            SdkUserSummaryRespVO result = new SdkUserSummaryRespVO();
            result.setTodayActiveUsers(activeUsers);
            return result;
        }
    }
    
    /**
     * 获取周活跃用户数，先检查权限再执行查询
     * 
     * @param reqVO 请求参数
     * @param permittedConditions 用户具有权限的产品ID和服务器名称组合条件
     * @return 周活跃用户数据
     */
    default SdkUserSummaryRespVO getWeeklyActiveUsersSummary(SdkUserSummaryReqVO reqVO, List<SdkOrderCondition> permittedConditions) {
        // 计算一周的时间范围
        Long[] timeRange = reqVO.getPayTime();
        Calendar calendar = Calendar.getInstance();
        
        if (timeRange != null && timeRange.length == 2) {
            calendar.setTimeInMillis(timeRange[0] * 1000L);
        }
        
        // 获取当前日期所在周的周一
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        int offset = dayOfWeek == Calendar.SUNDAY ? -6 : (Calendar.MONDAY - dayOfWeek);
        calendar.add(Calendar.DAY_OF_MONTH, offset);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Long startTime = calendar.getTimeInMillis() / 1000;
        
        // 计算周日的日期
        calendar.add(Calendar.DAY_OF_MONTH, 6);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Long endTime = calendar.getTimeInMillis() / 1000;
        if (permittedConditions == null || permittedConditions.isEmpty()) {
            // 查询周活跃用户数
            Long activeUsers = getDailyActiveUsersWithConditions(startTime, endTime, reqVO.getProductId(), null, null);

            // 封装结果
            SdkUserSummaryRespVO result = new SdkUserSummaryRespVO();
            result.setWeekActiveUsers(activeUsers);
            return result;
        }
        // 检查是否有搜索条件
        List<SdkOrderCondition> effectiveConditions = null;
        
        // 先检查用户的请求参数中是否指定了查询条件
        if (reqVO.getProductId() != null && !reqVO.getProductId().isEmpty()) {
            
            // 有指定查询条件，检查是否在权限范围内
            List<SdkOrderCondition> requestConditions = new ArrayList<>();
            

            // 只有产品ID的情况，需要用权限条件中的服务器名称
          if (reqVO.getProductId() != null && !reqVO.getProductId().isEmpty()) {
                // 从权限条件中提取与请求产品ID匹配的条件
                for (SdkOrderCondition permittedCondition : permittedConditions) {
                    if (reqVO.getProductId().contains(permittedCondition.getProductId())) {
                        requestConditions.add(permittedCondition);
                    }
                }
            }
            
            // 检查请求条件是否在权限范围内，只保留有权限的条件
            if (!requestConditions.isEmpty() && permittedConditions != null && !permittedConditions.isEmpty()) {
                effectiveConditions = new ArrayList<>();
                for (SdkOrderCondition requestCondition : requestConditions) {
                    for (SdkOrderCondition permittedCondition : permittedConditions) {
                        if (requestCondition.getProductId().equals(permittedCondition.getProductId()) && 
                            requestCondition.getServerName().equals(permittedCondition.getServerName())) {
                            effectiveConditions.add(requestCondition);
                            break;
                        }
                    }
                }
            }
        } else {
            // 没有指定查询条件，使用权限条件
            effectiveConditions = permittedConditions;
        }
        
        // 如果没有有效条件（可能是权限不足或者条件为空），返回空结果
        if (effectiveConditions == null || effectiveConditions.isEmpty()) {
            SdkUserSummaryRespVO emptyResult = new SdkUserSummaryRespVO();
            emptyResult.setWeekActiveUsers(0L);
            return emptyResult;
        }
        
        // 查询周活跃用户数
        Long activeUsers = getDailyActiveUsersWithConditions(startTime, endTime, null, null, effectiveConditions);
        
        // 封装结果
        SdkUserSummaryRespVO result = new SdkUserSummaryRespVO();
        result.setWeekActiveUsers(activeUsers);
        return result;
    }
    
    /**
     * 不带条件的方法版本，内部会使用用户权限
     */
    default SdkUserSummaryRespVO getWeeklyActiveUsersSummary(SdkUserSummaryReqVO reqVO) {
        // 此方法应该由 Service 层调用并传入权限条件
        // 在没有权限条件的情况下返回空结果
        SdkUserSummaryRespVO emptyResult = new SdkUserSummaryRespVO();
        emptyResult.setWeekActiveUsers(0L);
        return emptyResult;
    }
    
    /**
     * 支持权限SQL的重载方法
     */
    default SdkUserSummaryRespVO getWeeklyActiveUsersSummary(@Param("reqVO") SdkUserSummaryReqVO reqVO, @Param("permissionSql") String permissionSql) {
        // 此方法应该由 Service 层调用并传入权限条件
        // 在没有权限条件的情况下返回空结果
        SdkUserSummaryRespVO emptyResult = new SdkUserSummaryRespVO();
        emptyResult.setWeekActiveUsers(0L);
        return emptyResult;
    }
    
    /**
     * 支持权限SQL的重载方法（改为default实现）
     */
    default SdkUserSummaryRespVO getWeeklyActiveUsersSummaryWithPermission(@Param("reqVO") SdkUserSummaryReqVO reqVO, @Param("permissionSql") String permissionSql) {
        // 计算一周的时间范围
        Long[] timeRange = reqVO.getPayTime();
        Calendar calendar = Calendar.getInstance();
        
        if (timeRange != null && timeRange.length == 2) {
            calendar.setTimeInMillis(timeRange[0] * 1000L);
        }
        
        // 获取当前日期所在周的周一
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        int offset = dayOfWeek == Calendar.SUNDAY ? -6 : (Calendar.MONDAY - dayOfWeek);
        calendar.add(Calendar.DAY_OF_MONTH, offset);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Long startTime = calendar.getTimeInMillis() / 1000;
        
        // 计算周日的日期
        calendar.add(Calendar.DAY_OF_MONTH, 6);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Long endTime = calendar.getTimeInMillis() / 1000;
        
        // 如果有权限SQL，使用复杂的权限查询
        if (permissionSql != null && !permissionSql.trim().isEmpty()) {
            MPJLambdaWrapperX<SdkUserDO> queryWrapper = new MPJLambdaWrapperX<SdkUserDO>();
            queryWrapper.select("COUNT(DISTINCT t.uid) AS weekActiveUsers")
                       .leftJoin(SdkProductDO.class, SdkProductDO::getId, SdkUserDO::getProductId)
                       .leftJoin(SdkRoleDO.class, "r", on -> on.eq(SdkRoleDO::getUid, SdkUserDO::getUid))
                       .apply("EXISTS (SELECT 1 FROM qsdk_user_online online WHERE online.uid = t.uid AND online.createTime BETWEEN " + startTime + " AND " + endTime + ")")
                       .apply(permissionSql);
            
            return selectJoinOne(SdkUserSummaryRespVO.class, queryWrapper);
        } else {
            // 没有权限SQL时，使用原有逻辑
            Long activeUsers = getDailyActiveUsersWithConditions(startTime, endTime, reqVO.getProductId(), null, null);
            SdkUserSummaryRespVO result = new SdkUserSummaryRespVO();
            result.setWeekActiveUsers(activeUsers);
            return result;
        }
    }
    
    /**
     * 获取按天统计的活跃用户趋势
     */
    @Select("""
            <script>
            SELECT 
                days.day as time,
                IFNULL(t.active_count, 0) as price
            FROM (
                SELECT 1 as day UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION
                SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION
                SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
                SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION SELECT 20 UNION
                SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION SELECT 25 UNION
                SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION SELECT 30 UNION
                SELECT 31
            ) days
            LEFT JOIN (
                SELECT 
                    FROM_UNIXTIME(t.createTime, '%Y-%m-%d') as full_date,
                    FROM_UNIXTIME(t.createTime, '%d') as day,
                    COUNT(DISTINCT t.uid) as active_count
                FROM qsdk_user_online t
                JOIN qsdk_roles r ON t.uid = CONVERT(r.uid, UNSIGNED)
                WHERE t.createTime BETWEEN #{startTime} AND #{endTime}
                <if test="conditions != null and conditions.size() > 0">
                AND (
                    <foreach collection="conditions" item="condition" separator=" OR ">
                        (r.productId = #{condition.productId} AND r.serverName = #{condition.serverName}
                          <if test="condition.channelCode != null and condition.channelCode != ''">
                                                           and r.channelCode = #{condition.channelCode}
                                    </if>
                        )
                    </foreach>
                )
                </if>
                <if test="conditions == null">
                    <if test="productIds != null and productIds.size() > 0">
                    AND r.productId IN 
                    <foreach collection="productIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                    </if>
                    <if test="serverNames != null and serverNames.size() > 0">
                    AND r.serverName IN 
                    <foreach collection="serverNames" item="serverName" open="(" separator="," close=")">
                        #{serverName}
                    </foreach>
                    </if>
                </if>
                GROUP BY full_date, day
            ) t ON days.day = t.day
            WHERE days.day BETWEEN DAY(FROM_UNIXTIME(#{startTime})) AND DAY(FROM_UNIXTIME(#{endTime}))
                AND MONTH(FROM_UNIXTIME(#{startTime})) = MONTH(FROM_UNIXTIME(#{endTime}))
            ORDER BY days.day ASC
            </script>
            """)
    List<SdkUserSummaryMonthRespVO> getMonthlyActiveUsersTrend(
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("productIds") List<Long> productIds,
            @Param("serverNames") List<String> serverNames,
            @Param("conditions") List<SdkOrderCondition> conditions);

    /**
     * 获取按天统计的活跃用户趋势，支持权限SQL
     */
    @Select("""
            <script>
            SELECT 
                days.day as time,
                IFNULL(t.active_count, 0) as price
            FROM (
                SELECT 1 as day UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION
                SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION
                SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
                SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION SELECT 20 UNION
                SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION SELECT 25 UNION
                SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION SELECT 30 UNION
                SELECT 31
            ) days
            LEFT JOIN (
                SELECT 
                    FROM_UNIXTIME(online.createTime, '%Y-%m-%d') as full_date,
                    FROM_UNIXTIME(online.createTime, '%d') as day,
                    COUNT(DISTINCT t.uid) as active_count
                FROM qsdk_user_online online
                INNER JOIN qsdk_user t ON online.uid = t.uid
                LEFT JOIN qsdk_product p ON p.id = t.productId
                LEFT JOIN qsdk_roles r ON r.uid = t.uid
                WHERE online.createTime BETWEEN #{startTime} AND #{endTime}
                <if test="permissionSql != null and permissionSql.trim() != ''">
                    AND ${permissionSql}
                </if>
                GROUP BY full_date, day
            ) t ON days.day = t.day
            WHERE days.day BETWEEN DAY(FROM_UNIXTIME(#{startTime})) AND DAY(FROM_UNIXTIME(#{endTime}))
                AND MONTH(FROM_UNIXTIME(#{startTime})) = MONTH(FROM_UNIXTIME(#{endTime}))
            ORDER BY days.day ASC
            </script>
            """)
    List<SdkUserSummaryMonthRespVO> getMonthlyActiveUsersTrendWithPermissionSql(
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("permissionSql") String permissionSql);

    /**
     * 获取月活跃用户趋势，先检查权限再执行查询
     */
    default List<SdkUserSummaryMonthRespVO> getMonthlyActiveUsersTrend(
            SdkUserSummaryReqVO reqVO,
            List<SdkOrderCondition> permittedConditions) {
        // 默认初始化为当前月份
        Long startTime;
        Long endTime;
        
        Long[] payTime = reqVO.getPayTime();
        if (payTime != null && payTime.length == 2) {
            // 使用指定的时间范围
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(payTime[0] * 1000L);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startTime = calendar.getTimeInMillis() / 1000;
    
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.SECOND, -1);
            endTime = calendar.getTimeInMillis() / 1000;
        } else {
            // 默认使用当前月
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startTime = calendar.getTimeInMillis() / 1000;
            
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.SECOND, -1);
            endTime = calendar.getTimeInMillis() / 1000;
        }
        if (permittedConditions == null || permittedConditions.isEmpty()) {
            // 查询月活跃用户数
            return getMonthlyActiveUsersTrend(startTime, endTime, reqVO.getProductId(), null, null);
        }
        
        // 检查是否有搜索条件
        List<SdkOrderCondition> effectiveConditions = null;
        
        // 先检查用户的请求参数中是否指定了查询条件
        if (reqVO.getProductId() != null && !reqVO.getProductId().isEmpty()) {
            
            // 有指定查询条件，检查是否在权限范围内
            List<SdkOrderCondition> requestConditions = new ArrayList<>();
            

            // 只有产品ID的情况，需要用权限条件中的服务器名称
            if (reqVO.getProductId() != null && !reqVO.getProductId().isEmpty()) {
                // 从权限条件中提取与请求产品ID匹配的条件
                for (SdkOrderCondition permittedCondition : permittedConditions) {
                    if (reqVO.getProductId().contains(permittedCondition.getProductId())) {
                        requestConditions.add(permittedCondition);
                    }
                }
            }
            
            // 检查请求条件是否在权限范围内，只保留有权限的条件
            if (!requestConditions.isEmpty() && permittedConditions != null && !permittedConditions.isEmpty()) {
                effectiveConditions = new ArrayList<>();
                for (SdkOrderCondition requestCondition : requestConditions) {
                    for (SdkOrderCondition permittedCondition : permittedConditions) {
                        if (requestCondition.getProductId().equals(permittedCondition.getProductId()) && 
                            requestCondition.getServerName().equals(permittedCondition.getServerName())) {
                            effectiveConditions.add(requestCondition);
                            break;
                        }
                    }
                }
            }
        } else {
            // 没有指定查询条件，使用权限条件
            effectiveConditions = permittedConditions;
        }
        
        // 如果没有有效条件（可能是权限不足或者条件为空），返回空结果
        if (effectiveConditions == null || effectiveConditions.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 查询月活跃用户趋势
        return getMonthlyActiveUsersTrend(startTime, endTime, null, null, effectiveConditions);
    }
    
    /**
     * 不带条件的方法版本，应由service层传入权限条件
     */
    default List<SdkUserSummaryMonthRespVO> getMonthlyActiveUsersTrend(SdkUserSummaryReqVO reqVO) {
        // 此方法应该由 Service 层调用并传入权限条件
        // 在没有权限条件的情况下返回空结果
        return Collections.emptyList();
    }
    
    /**
     * 支持权限SQL的重载方法
     */
    default List<SdkUserSummaryMonthRespVO> getMonthlyActiveUsersTrend(@Param("reqVO") SdkUserSummaryReqVO reqVO, @Param("permissionSql") String permissionSql) {
        // 此方法应该由 Service 层调用并传入权限条件
        // 在没有权限条件的情况下返回空结果
        return Collections.emptyList();
    }

    /**
     * 支持权限SQL的重载方法（改为default实现）
     */
    default List<SdkUserSummaryMonthRespVO> getMonthlyActiveUsersTrendWithPermission(@Param("reqVO") SdkUserSummaryReqVO reqVO, @Param("permissionSql") String permissionSql) {
        // 默认初始化为当前月份
        Long startTime;
        Long endTime;
        
        Long[] payTime = reqVO.getPayTime();
        if (payTime != null && payTime.length == 2) {
            // 使用指定的时间范围
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(payTime[0] * 1000L);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startTime = calendar.getTimeInMillis() / 1000;
    
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.SECOND, -1);
            endTime = calendar.getTimeInMillis() / 1000;
        } else {
            // 默认使用当前月
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startTime = calendar.getTimeInMillis() / 1000;
            
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.SECOND, -1);
            endTime = calendar.getTimeInMillis() / 1000;
        }
        
        // 如果有权限SQL，使用原生SQL查询
        if (permissionSql != null && !permissionSql.trim().isEmpty()) {
            return getMonthlyActiveUsersTrendWithPermissionSql(startTime, endTime, permissionSql);
        } else {
            // 没有权限SQL时，使用原有逻辑
            return getMonthlyActiveUsersTrend(startTime, endTime, reqVO.getProductId(), null, null);
        }
    }

    /**
     * 获取运营用户活跃数据摘要，使用产品ID和渠道代码进行精确匹配
     *
     * @param pageReqVO 请求参数
     * @param conditions 产品ID和渠道代码的组合条件
     * @return 活跃用户数据摘要
     */
    default SdkUserLiveSummaryRespVO getOperationActiveUserSummary(SdkOrderSummaryReqVO pageReqVO, 
                                                             List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions) {
        // 初始化结果对象
        SdkUserLiveSummaryRespVO result = new SdkUserLiveSummaryRespVO();
        
        // 设置时间范围
        Long[] payTime = pageReqVO.getPayTime();
        Calendar calendar = Calendar.getInstance();
        
        // 今日开始时间
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Long todayStart = calendar.getTimeInMillis() / 1000;
        
        // 今日结束时间
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Long todayEnd = calendar.getTimeInMillis() / 1000;
        
        // 计算本周开始时间
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        int offset = dayOfWeek == Calendar.SUNDAY ? -6 : (Calendar.MONDAY - dayOfWeek);
        calendar.add(Calendar.DAY_OF_MONTH, offset);
        Long weekStart = calendar.getTimeInMillis() / 1000;
        
        // 计算本周结束时间
        calendar.add(Calendar.DAY_OF_MONTH, 6);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Long weekEnd = calendar.getTimeInMillis() / 1000;
        
        // 计算本月开始时间
        calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Long monthStart = calendar.getTimeInMillis() / 1000;
        
        // 计算本月结束时间
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Long monthEnd = calendar.getTimeInMillis() / 1000;
        
        // 查询今日活跃用户数
        Long todayActiveUsers = countOperationActiveUsers(todayStart, todayEnd, pageReqVO.getProductId(), pageReqVO.getChannelCode(), conditions);
        result.setTodayLive(todayActiveUsers);
        
        // 查询本周活跃用户数
        Long weekActiveUsers = countOperationActiveUsers(weekStart, weekEnd, pageReqVO.getProductId(), pageReqVO.getChannelCode(), conditions);
        result.setWeekLive(weekActiveUsers);
        
        // 查询本月活跃用户数
        List<SdkOrderSummaryMonthRespVO> monthActiveUsers = getOperationMonthlyActiveUsersTrend(pageReqVO,conditions);
        result.setMonthlyLive(monthActiveUsers);
        
        return result;
    }
    
    /**
     * 统计运营权限下指定时间范围内的活跃用户数，使用产品ID和渠道代码进行精确匹配
     */
    @Select("""
            <script>
            SELECT COUNT(DISTINCT u.uid) as activeUsers
            FROM qsdk_user_online t
            left join qsdk_user  u on t.uid = u.uid
            WHERE t.createTime BETWEEN #{startTime} AND #{endTime}
                <choose>
                    <when test="conditions != null and conditions.size() > 0">
                        AND (
                            <foreach collection="conditions" item="condition" separator=" OR ">
                                (t.productId = #{condition.productId} AND u.channelCode = #{condition.channelCode})
                            </foreach>
                        )
                    </when>
                    <otherwise>
                        <if test="productId != null and productId.size() > 0">
                        AND t.productId IN 
                        <foreach collection="productId" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                        </if>
                        <if test="channelCode != null and channelCode.size() > 0">
                        AND u.channelCode IN 
                        <foreach collection="channelCode" item="code" open="(" separator="," close=")">
                            #{code}
                        </foreach>
                        </if>
                    </otherwise>
                </choose>
            </script>
            """)
    Long countOperationActiveUsers(@Param("startTime") Long startTime, 
                            @Param("endTime") Long endTime, 
                            @Param("productId") List<Long> productId,
                            @Param("channelCode") List<String> channelCode,
                            @Param("conditions") List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions);

    /**
     * 获取运营人员的月度活跃用户趋势，使用产品ID和渠道代码进行精确匹配
     */
    @Select("""
            <script>
            SELECT 
                days.day as time,
                IFNULL(t.active_count, 0) as price
            FROM (
                SELECT 1 as day UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION
                SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION
                SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
                SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION SELECT 20 UNION
                SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION SELECT 25 UNION
                SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29 UNION SELECT 30 UNION
                SELECT 31
            ) days
            LEFT JOIN (
                SELECT 
                    FROM_UNIXTIME(t.createTime, '%Y-%m-%d') as full_date,
                    FROM_UNIXTIME(t.createTime, '%d') as day,
                    COUNT(DISTINCT u.uid) as active_count
                FROM qsdk_user_online t
                left join qsdk_user  u on t.uid = u.uid
                WHERE createTime BETWEEN #{startTime} AND #{endTime}
                <choose>
                    <when test="conditions != null and conditions.size() > 0">
                        AND (
                            <foreach collection="conditions" item="condition" separator=" OR ">
                                (t.productId = #{condition.productId} AND u.channelCode = #{condition.channelCode})
                            </foreach>
                        )
                    </when>
                    <otherwise>
                        <if test="productId != null and productId.size() > 0">
                        AND t.productId IN 
                        <foreach collection="productId" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                        </if>
                        <if test="channelCode != null and channelCode.size() > 0">
                        AND u.channelCode IN 
                        <foreach collection="channelCode" item="code" open="(" separator="," close=")">
                            #{code}
                        </foreach>
                        </if>
                    </otherwise>
                </choose>
                GROUP BY full_date, day
            ) t ON days.day = t.day
            WHERE days.day BETWEEN DAY(FROM_UNIXTIME(#{startTime})) AND DAY(FROM_UNIXTIME(#{endTime}))
                AND MONTH(FROM_UNIXTIME(#{startTime})) = MONTH(FROM_UNIXTIME(#{endTime}))
            ORDER BY days.day ASC
            </script>
            """)
    List<SdkOrderSummaryMonthRespVO> getOperationMonthlyActiveUsersTrend(
            @Param("startTime") Long startTime, 
            @Param("endTime") Long endTime, 
            @Param("productId") List<Long> productId,
            @Param("channelCode") List<String> channelCode,
            @Param("conditions") List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions);
    
    /**
     * 获取运营月活跃用户趋势，先检查权限再执行查询
     */
    default List<SdkOrderSummaryMonthRespVO> getOperationMonthlyActiveUsersTrend(
            SdkOrderSummaryReqVO pageReqVO,
            List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> permittedConditions) {
        // 默认初始化为当前月份
        Long startTime;
        Long endTime;
        
        Long[] payTime = pageReqVO.getPayTime();
        if (payTime != null && payTime.length == 2) {
            // 使用指定的时间范围
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(payTime[0] * 1000L);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startTime = calendar.getTimeInMillis() / 1000;
    
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.SECOND, -1);
            endTime = calendar.getTimeInMillis() / 1000;
        } else {
            // 默认使用当前月
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startTime = calendar.getTimeInMillis() / 1000;
            
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.SECOND, -1);
            endTime = calendar.getTimeInMillis() / 1000;
        }
        
        // 检查是否有运营条件
        if (permittedConditions == null || permittedConditions.isEmpty()) {
            // 没有指定条件，使用产品ID和渠道代码直接查询
            return getOperationMonthlyActiveUsersTrend(startTime, endTime, pageReqVO.getProductId(), pageReqVO.getChannelCode(), null);
        } else {
            // 使用运营条件查询
            return getOperationMonthlyActiveUsersTrend(startTime, endTime, null, null, permittedConditions);
        }
    }
}