package com.mandong.api.module.erp.dto.permission;

import com.mandong.api.module.erp.enums.RoleTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 订单权限DTO
 */
@Data
public class OrderPermissionDTO {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 角色类型
     */
    private RoleTypeEnum roleType;
    
    /**
     * 可访问的用户ID列表
     */
    private List<Long> accessibleUserIds;
    
    /**
     * 可访问的产品ID列表
     */
    private List<Long> accessibleProductIds;
    
    /**
     * 可访问的渠道列表
     */
    private List<String> accessibleChannels;
    
    /**
     * 可访问的区服列表
     */
    private List<String> accessibleServers;
    
    /**
     * 是否有全部权限
     */
    private Boolean hasAllPermission;
} 