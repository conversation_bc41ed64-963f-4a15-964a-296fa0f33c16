package com.mandong.api.module.erp.dto.permission;

import com.mandong.api.module.erp.enums.RoleTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 用户层级信息DTO
 */
@Data
public class UserHierarchyDTO {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 角色类型
     */
    private RoleTypeEnum roleType;
    
    /**
     * 用户所属部门ID列表
     */
    private List<Long> departmentIds;
    
    /**
     * 上级用户ID
     */
    private Long parentUserId;
    
    /**
     * 下级用户ID列表
     */
    private List<Long> subordinateIds;
} 