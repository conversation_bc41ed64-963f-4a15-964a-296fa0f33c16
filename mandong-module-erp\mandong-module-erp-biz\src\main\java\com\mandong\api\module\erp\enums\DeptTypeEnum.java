package com.mandong.api.module.erp.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 部门业务类型枚举
 */
@Getter
@AllArgsConstructor
public enum DeptTypeEnum {

    /**
     * 运营部门
     */
    OPERATION("OPERATION", "运营部门"),

    /**
     * 运维部门
     */
    OPS("OPS", "运维部门");

    /**
     * 部门类型代码
     */
    private final String code;

    /**
     * 部门类型名称
     */
    private final String name;

    /**
     * 根据代码获取枚举
     */
    public static DeptTypeEnum getByCode(String code) {
        for (DeptTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 判断是否为运营部门
     */
    public static boolean isOperation(String deptType) {
        return OPERATION.getCode().equals(deptType);
    }

    /**
     * 判断是否为运维部门
     */
    public static boolean isOps(String deptType) {
        return OPS.getCode().equals(deptType);
    }
} 