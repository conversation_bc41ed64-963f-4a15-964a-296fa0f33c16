package com.mandong.api.module.erp.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 角色类型枚举
 */
@Getter
@AllArgsConstructor
public enum RoleTypeEnum {
    
    /**
     * 主管
     */
    SUPERVISOR(1, "主管"),
    
    /**
     * 组长
     */
    TEAM_LEADER(2, "组长"),
    
    /**
     * 组员
     */
    TEAM_MEMBER(3, "组员");
    
    /**
     * 角色级别
     */
    private final Integer level;
    
    /**
     * 角色描述
     */
    private final String description;
    
    /**
     * 根据级别获取角色类型
     */
    public static RoleTypeEnum getByLevel(Integer level) {
        if (level == null) {
            return TEAM_MEMBER;
        }
        
        for (RoleTypeEnum roleType : values()) {
            if (roleType.getLevel().equals(level)) {
                return roleType;
            }
        }
        
        return TEAM_MEMBER;
    }
} 