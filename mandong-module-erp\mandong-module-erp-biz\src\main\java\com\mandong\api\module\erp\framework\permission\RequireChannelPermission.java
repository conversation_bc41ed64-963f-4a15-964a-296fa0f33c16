package com.mandong.api.module.erp.framework.permission;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 渠道权限检查注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireChannelPermission {
    boolean checkAllChannels() default false;
    int channelCodeParamIndex() default 0;
    String description() default "渠道访问权限";
} 