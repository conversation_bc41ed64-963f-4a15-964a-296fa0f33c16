package com.mandong.api.module.erp.framework.permission;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 运营队伍权限检查注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireOperationTeamPermission {
    boolean checkAllTeams() default false;
    int operationTeamParamIndex() default 0;
    String description() default "运营队伍访问权限";
} 