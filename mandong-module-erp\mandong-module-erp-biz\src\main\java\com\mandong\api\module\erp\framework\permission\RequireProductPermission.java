package com.mandong.api.module.erp.framework.permission;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 产品权限检查注解
 * 用于标记需要检查产品访问权限的方法
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireProductPermission {
    
    /**
     * 是否检查所有产品权限
     * true: 只检查用户是否有任何产品权限
     * false: 检查用户对特定产品的权限
     */
    boolean checkAllProducts() default false;
    
    /**
     * 产品ID参数在方法参数中的索引位置
     * 当checkAllProducts为false时使用
     */
    int productIdParamIndex() default 0;
    
    /**
     * 权限描述
     */
    String description() default "产品访问权限";
} 