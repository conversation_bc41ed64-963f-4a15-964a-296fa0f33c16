package com.mandong.api.module.erp.framework.permission;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 区服权限检查注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireServerPermission {
    boolean checkAllServers() default false;
    int serverNameParamIndex() default 0;
    String description() default "区服访问权限";
} 