package com.mandong.api.module.erp.job;

import cn.hutool.core.date.DateTime;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.framework.quartz.core.handler.JobHandler;
import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderCallgame;
import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import com.mandong.api.module.erp.dal.mysql.order.SdkOrderCallgameMapper;
import com.mandong.api.module.erp.dal.redis.RedisKeyConstants;
import com.mandong.api.module.erp.dal.sdkMysql.order.SdkOrderMapper;
import com.mandong.api.module.erp.dal.sdkMysql.product.SdkProductMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class OrderPayNotifyJob implements JobHandler {

    @Resource
    private SdkOrderMapper sdkOrderMapper;

    @Resource
    private SdkOrderCallgameMapper orderCallgameMapper;

    @Resource
    private SdkProductMapper sdkProductMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    // 重试延迟时间配置（秒）：10秒、30秒、1分钟、1分半、2分钟、5分钟、10分钟
    private static final int[] RETRY_DELAYS = {10, 30, 60, 90, 120, 300, 600};
    private static final int MAX_RETRY_COUNT = 7;


    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public String execute(String param) {
        if (stringRedisTemplate.opsForValue().get("OrderPayNotifyJob")!=null){
             return "";
        }
        try {
            stringRedisTemplate.opsForValue().setIfAbsent("OrderPayNotifyJob","1",2, TimeUnit.MINUTES);
            log.info("定时任务：订单支付发生道具通知 开始========================");
            List<SdkOrderDO> orderDOS = sdkOrderMapper.selectList(new MPJLambdaWrapperX<SdkOrderDO>().like(SdkOrderDO::getOrderNo, "W").eq(SdkOrderDO::getPayStatus, "1").eq(SdkOrderDO::getAsyncStatus, "0"));

            if (orderDOS.isEmpty()){
                return "success";
            }

            orderDOS.forEach(orderDO -> {
                // 检查是否应该重试
                if (!shouldRetry(orderDO.getOrderNo())) {
                    return;
                }

                // 记录重试尝试
                int retryCount = recordRetryAttempt(orderDO.getOrderNo());

                log.info("订单号：{},第{}次重试,回调地址：{}，回调参数：{}",
                        orderDO.getOrderNo(), retryCount, orderDO.getCallbackUrl(), orderDO.getExtrasParams());
                SdkProductDO sdkProductDO = sdkProductMapper.selectOne(SdkProductDO::getId, orderDO.getProductId());
                if (sdkProductDO == null) {
                    log.error("游戏不存在");
                    return;
                }
                SdkOrderCallgame sdkOrderCallgame = new SdkOrderCallgame();
                sdkOrderCallgame.setOrderno(orderDO.getOrderNo());
                sdkOrderCallgame.setCallparams(orderDO.getExtrasParams());
                sdkOrderCallgame.setCallurl(orderDO.getCallbackUrl());
                sdkOrderCallgame.setCreatetime(new DateTime().toTimestamp().getTime() / 1000);
                // 使用带签名的POST请求
                String result = sendSignedPostRequest(orderDO.getCallbackUrl(), orderDO, sdkProductDO);
                if (result.equals("success")) {
                    orderDO.setAsyncStatus("1");
                    sdkOrderMapper.updateById(orderDO);
                    sdkOrderCallgame.setStatus("SUCCESS");

                    // 成功后清除重试缓存
                    clearRetryCache(orderDO.getOrderNo());
                    log.info("订单号：{}，回调成功，已清除重试缓存", orderDO.getOrderNo());
                } else {
                    sdkOrderCallgame.setStatus(result);
                    log.warn("订单号：{}，回调失败，将在下次定时任务中重试", orderDO.getOrderNo());
                }
                orderCallgameMapper.insert(sdkOrderCallgame);
            });
        }catch (Exception e){
            log.error("定时任务：订单支付发生道具通知 异常========================",e);
            throw e;
        }finally {
            stringRedisTemplate.delete("OrderPayNotifyJob");
            log.info("定时任务：订单支付发生道具通知 结束========================");
        }


        return "success";
    }

    /**
     * 发送带签名的POST请求，参考前端代码的加密方式
     *
     * @param serverUrl 服务器URL
     * @param orderDO 订单信息
     * @param productDO 产品信息
     */
    private String sendSignedPostRequest(String serverUrl, SdkOrderDO orderDO, SdkProductDO productDO) {
        try {
            // 准备请求参数 - 参考前端代码
            Map<String, Object> params = new TreeMap<>(); // 使用TreeMap自动排序
            params.put("uid", orderDO.getUid());
            params.put("username", orderDO.getUsername());
            params.put("cpOrderNo", orderDO.getChannelOrderNo());
            params.put("orderNo", orderDO.getOrderNo());
            params.put("payTime", new DateTime(orderDO.getPayTime() * 1000).toString("yyyy-MM-dd HH:mm:ss"));
            params.put("payType", orderDO.getPayType());
            params.put("payAmount", orderDO.getDealAmount());
            params.put("payCurrency", orderDO.getCurrencyWord());
            params.put("usdAmount", orderDO.getDealUsdAmount());
            params.put("payStatus", "0");
            params.put("actRate", orderDO.getActRate());
            params.put("extrasParams", orderDO.getExtrasParams());


            // 计算签名 - 使用与前端相同的签名算法
            String signKey = buildSignKey(params, productDO.getCallbackKey());

            // 计算MD5值
            String sign = SecureUtil.md5(signKey);

            // 添加签名到参数中
            params.put("sign", sign);

            log.info("发送签名请求到：{}，参数：{}", serverUrl, params);

            // 发送POST请求 - 使用form-data格式
            try (HttpResponse response = HttpRequest.post(serverUrl)
                    .form(new HashMap<>(params)) // 转换为HashMap<String, Object>
                    .execute()) {

                String responseBody = response.body();
                log.info("请求响应：{}", responseBody);

                if (response.isOk()) {
                    if (responseBody.equals("SUCCESS")) {
                        return "success";
                    }
                    log.info("签名请求发送成功");
                } else {
                    log.error("签名请求发送失败，状态码：{}", response.getStatus());
                }
            }

        } catch (Exception e) {
            log.error("发送签名请求异常：", e);
        }
        return "failed";
    }

    /**
     * 构建签名字符串
     *
     * @param params 参数Map
     * @param callbackKey 回调密钥
     * @return 签名字符串
     */
    private String buildSignKey(Map<String, Object> params, String callbackKey) {
        StringBuilder signKey = new StringBuilder();

        // 按key排序并拼接参数
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            signKey.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }

        // 添加callbackKey
        signKey.append(callbackKey);

        String result = signKey.toString();
        log.debug("签名字符串：{}", result);

        return result;
    }

    /**
     * 检查订单是否应该重试
     */
    private boolean shouldRetry(String orderNo) {
        String key = RedisKeyConstants.ORDER_RETRY + orderNo;
        String retryInfo = stringRedisTemplate.opsForValue().get(key);

        if (retryInfo == null) {
            // 第一次重试，直接允许
            return true;
        }

        String[] parts = retryInfo.split(":");
        int retryCount = Integer.parseInt(parts[0]);
        long lastRetryTime = Long.parseLong(parts[1]);

        // 检查是否超过最大重试次数
        if (retryCount >= MAX_RETRY_COUNT) {
            log.info("订单号：{}，已达到最大重试次数{}，跳过处理", orderNo, MAX_RETRY_COUNT);
            return false;
        }

        // 计算应该等待的时间
        if (retryCount > 0 && retryCount <= RETRY_DELAYS.length) {
            int delaySeconds = RETRY_DELAYS[retryCount - 1];
            long currentTime = System.currentTimeMillis() / 1000;
            long waitTime = currentTime - lastRetryTime;

            if (waitTime < delaySeconds) {
                log.info("订单号：{}，第{}次重试需要等待{}秒，当前已等待{}秒，跳过本次处理",
                        orderNo, retryCount + 1, delaySeconds, waitTime);
                return false;
            }
        }

        return true;
    }

    /**
     * 记录重试尝试
     */
    private int recordRetryAttempt(String orderNo) {
        String key = RedisKeyConstants.ORDER_RETRY + orderNo;
        String retryInfo = stringRedisTemplate.opsForValue().get(key);

        int retryCount = 1;
        if (retryInfo != null) {
            String[] parts = retryInfo.split(":");
            retryCount = Integer.parseInt(parts[0]) + 1;
        }

        long currentTime = System.currentTimeMillis() / 1000;
        String newRetryInfo = retryCount + ":" + currentTime;

        // 设置24小时过期
        stringRedisTemplate.opsForValue().set(key, newRetryInfo, 24 * 60 * 60, TimeUnit.SECONDS);

        log.info("订单号：{}，记录第{}次重试尝试", orderNo, retryCount);
        return retryCount;
    }

    /**
     * 清除重试缓存
     */
    private void clearRetryCache(String orderNo) {
        String key = RedisKeyConstants.ORDER_RETRY + orderNo;
        stringRedisTemplate.delete(key);
        log.info("订单号：{}，清除重试缓存", orderNo);
    }

}
