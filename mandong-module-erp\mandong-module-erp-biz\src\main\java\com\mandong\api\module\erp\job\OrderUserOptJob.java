package com.mandong.api.module.erp.job;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.mandong.api.framework.quartz.core.handler.JobHandler;
import com.mandong.api.module.erp.dal.sdkMysql.opt.SdkOptMapper;
import com.mandong.api.module.erp.job.vo.OrderUserOptRespVO;
import com.mandong.api.module.erp.util.MdKiller;
import com.mandong.api.module.infra.api.file.FileApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

@Component
@Slf4j
public class OrderUserOptJob implements JobHandler {

    @Resource
    private SdkOptMapper sdkOptMapper;
    @Resource
    private FileApi fileApi;

    @Override
    public String execute(String param) throws Exception {

        List<OrderUserOptRespVO> orderUserOptRespVOS = sdkOptMapper.selectOrderUserOptMonitor();
        for (OrderUserOptRespVO orderUserOptRespVO : orderUserOptRespVOS) {
            orderUserOptRespVO.setLoginFlag(orderUserOptRespVO.getLoginFlag().equals("1")?"✅":"❌");
            orderUserOptRespVO.setPayFlag(orderUserOptRespVO.getPayFlag().equals("1")?"✅":"❌");
        }

        // 生成Excel文件
        ExcelWriter writer = ExcelUtil.getWriter();

        writer.merge(8, "大哥流失预警(支付金额>100W 交易时间45天以内)");
        writer.write(orderUserOptRespVOS, true);
        writer.autoSizeColumnAll();
        writer.autoSizeColumn(7, 2.0F);
        writer.autoSizeColumn(8, 2.0F);
        // 将ExcelWriter转换为byte数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        writer.flush(outputStream);
        byte[] excelBytes = outputStream.toByteArray();

        // 生成文件名（包含时间戳）
        String fileName = "大哥流失预警_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";

        // 上传到OSS
        String fileUrl = fileApi.createFile(fileName, "temp", excelBytes);
        log.info("Excel文件已上传到OSS，访问地址：{}", fileUrl);

        // 关闭writer，释放内存
        writer.close();
        outputStream.close();

        // 发送钉钉消息
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/robot/send?access_token=d44a64b6efeaafbdaf28ec45b6c1c0fa950e812da376d43b1b0412657649e810");
        OapiRobotSendRequest req = new OapiRobotSendRequest();
        req.setMsgtype("markdown");
        OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
        markdown.setTitle("大哥预警(支付金额>100W 交易时间45天以内)");

        // 构建Markdown内容，包含Excel文件下载链接
        MdKiller.SectionBuilder text = MdKiller.of().title("️今日大哥流失预警，请关注！！", MdKiller.Style.RED)
                .text("📊 Excel报表已生成，点击下载：[" + fileName + "](" + fileUrl + ") \n");




        text.text("共 " + orderUserOptRespVOS.size() + " 条数据，完整数据请下载Excel文件查看");

        markdown.setText(text.build());
        req.setMarkdown(markdown);

        OapiRobotSendResponse response = client.execute(req);
        log.info("钉钉消息发送结果：{}", response.getBody());

        return "执行成功，共处理 " + orderUserOptRespVOS.size() + " 条数据，Excel文件地址：" + fileUrl;
    }




}
