package com.mandong.api.module.erp.job.vo;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

@Data
public class OrderUserOptRespVO {
    @Alias(value = "用户id")
    private String uid;

    @Alias(value = "游戏")
    private String productName;

    @Alias(value = "用户名")
    private String userName;


    @Alias(value = "运营人员")
    private String optName ="CN";

    @Alias(value = "最后登录时间")
    private String lastLoginTime;

    @Alias(value = "最后支付时间")
    private String lastPayTime;

    @Alias(value = "总支付金额")
    private Float dealAmount;

    @Alias(value = "登录预警(3天)")
    private String loginFlag;

    @Alias(value = "支付预警(3天)")
    private String payFlag;
}
