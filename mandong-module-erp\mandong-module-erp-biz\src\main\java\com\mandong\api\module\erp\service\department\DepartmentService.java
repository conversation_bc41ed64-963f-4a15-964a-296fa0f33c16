package com.mandong.api.module.erp.service.department;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.department.vo.*;
import com.mandong.api.module.erp.dal.dataobject.department.DepartmentDO;

import java.util.List;

/**
 * 部门管理 Service 接口
 */
public interface DepartmentService {
    
    /**
     * 创建部门
     */
    Long createDepartment(DepartmentSaveReqVO createReqVO);
    
    /**
     * 更新部门
     */
    void updateDepartment(DepartmentSaveReqVO updateReqVO);
    
    /**
     * 删除部门
     */
    void deleteDepartment(Long id);
    
    /**
     * 获得部门
     */
    DepartmentDO getDepartment(Long id);
    
    /**
     * 获得部门分页
     */
    PageResult<DepartmentRespVO> getDepartmentPage(DepartmentPageReqVO pageReqVO);
    
    /**
     * 获得部门树形结构
     */
    List<DepartmentRespVO> getDepartmentTree();
    
    /**
     * 获得部门列表
     */
    List<DepartmentDO> getDepartmentList(DepartmentPageReqVO reqVO);
    
    /**
     * 校验部门是否存在
     */
    void validateDepartmentExists(Long id);
    
    /**
     * 校验部门名称是否重复
     */
    void validateDepartmentNameUnique(Long id, Long parentId, String name);
    
    /**
     * 校验是否为父部门的子部门
     */
    void validateNotParentDept(Long id, Long parentId);
    
    /**
     * 设置用户部门关系
     */
    void setUserDepartments(UserDeptRelationVO relationVO);
    
    /**
     * 获取用户的部门列表
     */
    List<DepartmentRespVO> getUserDepartments(Long userId);
    
    /**
     * 移除用户部门关系
     */
    void removeUserFromDepartment(Long userId, Long deptId);
    
    /**
     * 获取部门下的用户列表
     */
    List<UserDeptRelationVO> getDepartmentUsers(Long deptId);
} 