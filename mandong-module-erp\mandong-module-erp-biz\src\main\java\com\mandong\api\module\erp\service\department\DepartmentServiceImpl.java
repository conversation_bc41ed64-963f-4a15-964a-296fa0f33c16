package com.mandong.api.module.erp.service.department;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mandong.api.framework.common.exception.util.ServiceExceptionUtil;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mandong.api.framework.mybatis.core.query.QueryWrapperX;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.controller.admin.department.vo.*;
import com.mandong.api.module.erp.dal.dataobject.department.DepartmentDO;
import com.mandong.api.module.erp.dal.dataobject.department.UserDeptRelationDO;
import com.mandong.api.module.erp.dal.sdkMysql.department.DepartmentMapper;
import com.mandong.api.module.erp.dal.sdkMysql.department.UserDeptRelationMapper;
import com.mandong.api.module.erp.enums.RoleTypeEnum;
import com.mandong.api.module.system.api.permission.PermissionApi;
import com.mandong.api.module.system.api.user.AdminUserApi;
import com.mandong.api.module.system.api.user.dto.AdminUserRespDTO;
import com.mandong.api.module.system.enums.permission.RoleCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.mandong.api.module.erp.enums.ErrorCodeConstants.*;

/**
 * 部门管理 Service 实现类
 */
@Service
@Slf4j
public class DepartmentServiceImpl implements DepartmentService {

    @Resource
    private DepartmentMapper departmentMapper;
    
    @Resource
    private UserDeptRelationMapper userDeptRelationMapper;
    
    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private PermissionApi permissionApi;

    @Override
    @DS("sdkDB")
    @Transactional(rollbackFor = Exception.class)
    public Long createDepartment(DepartmentSaveReqVO createReqVO) {
        // 校验父部门是否存在
        validateParentDepartment(createReqVO.getParentId());
        
        // 校验部门名称是否重复
        validateDepartmentNameUnique(null, createReqVO.getParentId(), createReqVO.getDeptName());
        
        // 创建部门
        DepartmentDO department = DepartmentDO.builder()
                .parentId(createReqVO.getParentId())
                .deptName(createReqVO.getDeptName())
                .deptCode(createReqVO.getDeptCode())
                .deptLevel(createReqVO.getDeptLevel())
                .sortOrder(createReqVO.getSortOrder() != null ? createReqVO.getSortOrder() : 0)
                .status(createReqVO.getStatus())
                .description(createReqVO.getDescription())
                .creator(SecurityFrameworkUtils.getLoginUserNickname())
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
        
        departmentMapper.insert(department);
        return department.getId();
    }

    @Override
    @DS("sdkDB")
    @Transactional(rollbackFor = Exception.class)
    public void updateDepartment(DepartmentSaveReqVO updateReqVO) {
        // 校验部门是否存在
        validateDepartmentExists(updateReqVO.getId());
        
        // 校验父部门是否存在
        validateParentDepartment(updateReqVO.getParentId());
        
        // 校验不能设置自己为父部门
        validateNotParentDept(updateReqVO.getId(), updateReqVO.getParentId());
        
        // 校验部门名称是否重复
        validateDepartmentNameUnique(updateReqVO.getId(), updateReqVO.getParentId(), updateReqVO.getDeptName());
        
        // 更新部门
        DepartmentDO updateObj = DepartmentDO.builder()
                .id(updateReqVO.getId())
                .parentId(updateReqVO.getParentId())
                .deptName(updateReqVO.getDeptName())
                .deptCode(updateReqVO.getDeptCode())
                .deptLevel(updateReqVO.getDeptLevel())
                .sortOrder(updateReqVO.getSortOrder() != null ? updateReqVO.getSortOrder() : 0)
                .status(updateReqVO.getStatus())
                .description(updateReqVO.getDescription())
                .updater(SecurityFrameworkUtils.getLoginUserNickname())
                .updateTime(LocalDateTime.now())
                .build();
        
        departmentMapper.updateById(updateObj);
    }

    @Override
    @DS("sdkDB")
    @Transactional(rollbackFor = Exception.class)
    public void deleteDepartment(Long id) {
        // 校验部门是否存在
        validateDepartmentExists(id);
        
        // 校验是否有子部门
        validateNoChildDepartments(id);
        
        // 校验是否有用户关联
        validateNoUserRelations(id);
        
        // 删除部门
        departmentMapper.deleteById(id);
    }

    @Override
    @DS("sdkDB")
    public DepartmentDO getDepartment(Long id) {
        return departmentMapper.selectById(id);
    }

    @Override
    @DS("sdkDB")
    public PageResult<DepartmentRespVO> getDepartmentPage(DepartmentPageReqVO pageReqVO) {
        QueryWrapper<DepartmentDO> queryWrapper = buildQueryWrapper(pageReqVO);
        
        IPage<DepartmentDO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        IPage<DepartmentDO> resultPage = departmentMapper.selectPage(page, queryWrapper);
        
        List<DepartmentRespVO> list = resultPage.getRecords().stream()
                .map(this::convertToRespVO)
                .collect(Collectors.toList());
        
        return new PageResult<>(list, resultPage.getTotal());
    }

    @Override
    @DS("sdkDB")
    public List<DepartmentRespVO> getDepartmentTree() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        QueryWrapper<DepartmentDO> departmentDOQueryWrapper=new QueryWrapper<DepartmentDO>()
                .eq("status", 1)
                .orderByAsc("sort_order", "id");
      
        List<DepartmentDO> departments = departmentMapper.selectList(departmentDOQueryWrapper);
        
        List<DepartmentRespVO> respVOList = departments.stream()
                .map(this::convertToRespVO)
                .collect(Collectors.toList());
        
        return buildDepartmentTree(respVOList);
    }

    private boolean isSuperAdmin(Long userId) {
        // 方法1：通过用户ID判断（通常管理员用户ID为1）
        if (userId.equals(1L)) {
            return true;
        }

        // 方法2：通过角色判断
        if (permissionApi != null) {
            return permissionApi.hasAnyRoles(userId, RoleCodeEnum.SUPER_ADMIN.getCode());
        }
        return false;
    }

    @Override
    @DS("sdkDB")
    public List<DepartmentDO> getDepartmentList(DepartmentPageReqVO reqVO) {
        QueryWrapper<DepartmentDO> queryWrapper = buildQueryWrapper(reqVO);
        return departmentMapper.selectList(queryWrapper);
    }

    @Override
    @DS("sdkDB")
    public void validateDepartmentExists(Long id) {
        if (id == null) {
            return;
        }
        DepartmentDO department = departmentMapper.selectById(id);
        if (department == null) {
            throw ServiceExceptionUtil.exception(DEPARTMENT_NOT_EXISTS);
        }
    }

    @Override
    @DS("sdkDB")
    public void validateDepartmentNameUnique(Long id, Long parentId, String name) {
        if (!StringUtils.hasText(name)) {
            return;
        }

        LambdaQueryWrapperX<DepartmentDO> queryWrapperX = new LambdaQueryWrapperX<DepartmentDO>().eq(DepartmentDO::getParentId, parentId)
                .eq(DepartmentDO::getDeptName, name).eq(DepartmentDO::getStatus, 1);

        if (id != null) {
            queryWrapperX.ne(DepartmentDO::getId, id);
        }

        DepartmentDO department = departmentMapper.selectOne(queryWrapperX);
        if (department != null) {
            throw ServiceExceptionUtil.exception(DEPARTMENT_NAME_DUPLICATE);
        }
    }

    @Override
    @DS("sdkDB")
    public void validateNotParentDept(Long id, Long parentId) {
        if (id == null || parentId == null) {
            return;
        }
        if (id.equals(parentId)) {
            throw ServiceExceptionUtil.exception(DEPARTMENT_PARENT_IS_CHILD);
        }
        
        // 递归校验父部门
        DepartmentDO parent = departmentMapper.selectById(parentId);
        if (parent != null && parent.getParentId() != null && parent.getParentId() != 0) {
            validateNotParentDept(id, parent.getParentId());
        }
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public void setUserDepartments(UserDeptRelationVO relationVO) {
        // 先获取用户信息（使用默认数据源）
        AdminUserRespDTO user = null;
        try {
            user = adminUserApi.getUser(relationVO.getUserId());
        } catch (Exception e) {
            log.warn("获取用户信息失败: userId={}, error={}", relationVO.getUserId(), e.getMessage());
        }
        
        // 校验用户是否已在其他部门中
        validateUserNotInOtherDepartments(relationVO.getUserId(), relationVO.getDeptIds(), user);
        
        // 先删除用户现有的部门关系
        userDeptRelationMapper.delete(
            new QueryWrapper<UserDeptRelationDO>()
                .eq("user_id", relationVO.getUserId())
                .eq("deleted", 0)
        );
        
        // 添加新的部门关系
        if (!CollectionUtils.isEmpty(relationVO.getDeptIds())) {
            List<UserDeptRelationDO> relations = relationVO.getDeptIds().stream()
                    .map(deptId -> UserDeptRelationDO.builder()
                            .userId(relationVO.getUserId())
                            .deptId(deptId)
                            .relationType(relationVO.getRelationType() != null ? relationVO.getRelationType() : 1)
                            .status(relationVO.getStatus() != null ? relationVO.getStatus() : 1)
                            .deleted(0)
                            .creator(SecurityFrameworkUtils.getLoginUserNickname())
                            .createTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .build())
                    .collect(Collectors.toList());
            
            relations.forEach(relation -> userDeptRelationMapper.insert(relation));
        }
    }

    @Override
    @DS("sdkDB")
    public List<DepartmentRespVO> getUserDepartments(Long userId) {
        // 查询用户部门关系
        List<UserDeptRelationDO> relations = userDeptRelationMapper.selectList(
            new QueryWrapper<UserDeptRelationDO>()
                .eq("user_id", userId)
                .eq("status", 1)
                .eq("deleted", 0)
        );
        
        if (CollectionUtils.isEmpty(relations)) {
            return Collections.emptyList();
        }
        
        // 查询部门信息
        List<Long> deptIds = relations.stream()
                .map(UserDeptRelationDO::getDeptId)
                .collect(Collectors.toList());
        
        List<DepartmentDO> departments = departmentMapper.selectBatchIds(deptIds);
        
        return departments.stream()
                .map(this::convertToRespVO)
                .collect(Collectors.toList());
    }

    @Override
    @DS("sdkDB")
    @Transactional(rollbackFor = Exception.class)
    public void removeUserFromDepartment(Long userId, Long deptId) {
        userDeptRelationMapper.delete(
            new QueryWrapper<UserDeptRelationDO>()
                .eq("user_id", userId)
                .eq("dept_id", deptId)
                .eq("deleted", 0)
        );
    }

    @Override
    public List<UserDeptRelationVO> getDepartmentUsers(Long deptId) {
        // 查询部门用户关系列表
        List<UserDeptRelationDO> relations = userDeptRelationMapper.selectList(
            new QueryWrapper<UserDeptRelationDO>()
                .eq("dept_id", deptId)
                .eq("status", 1)
                .eq("deleted", 0)
        );
        
        if (CollectionUtils.isEmpty(relations)) {
            return Collections.emptyList();
        }
        
        // 获取用户ID列表
        List<Long> userIds = relations.stream()
                .map(UserDeptRelationDO::getUserId)
                .collect(Collectors.toList());
        
        // 通过系统用户API获取用户详细信息
        List<AdminUserRespDTO> users = adminUserApi.getUserList(userIds);
        log.info("getUserList调用结果: userIds={}, 返回用户数量={}", userIds, users != null ? users.size() : 0);
        
        Map<Long, AdminUserRespDTO> userMap = users.stream()
                .collect(Collectors.toMap(AdminUserRespDTO::getId, user -> user));

        // 转换为VO对象并填充用户信息
        return relations.stream()
                .map(relation -> {
                    UserDeptRelationVO vo = convertToUserDeptRelationVO(relation);
                    AdminUserRespDTO user = userMap.get(relation.getUserId());
                    if (user != null) {
                        String nickname = user.getNickname();
                        log.info("用户{}的昵称: {}", relation.getUserId(), nickname);
                        vo.setUserName(nickname); // 使用昵称作为用户名显示
                    } else {
                        log.warn("未找到用户ID={}的详细信息", relation.getUserId());
                        vo.setUserName("用户" + relation.getUserId()); // 备用显示
                    }
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper<DepartmentDO> buildQueryWrapper(DepartmentPageReqVO reqVO) {
        QueryWrapper<DepartmentDO> queryWrapper = new QueryWrapper<>();
        
        if (StringUtils.hasText(reqVO.getDeptName())) {
            queryWrapper.like("dept_name", reqVO.getDeptName());
        }
        if (StringUtils.hasText(reqVO.getDeptCode())) {
            queryWrapper.like("dept_code", reqVO.getDeptCode());
        }
        if (reqVO.getDeptLevel() != null) {
            queryWrapper.eq("dept_level", reqVO.getDeptLevel());
        }
        if (reqVO.getParentId() != null) {
            queryWrapper.eq("parent_id", reqVO.getParentId());
        }
        if (reqVO.getStatus() != null) {
            queryWrapper.eq("status", reqVO.getStatus());
        }
        
        queryWrapper.orderByAsc("sort_order", "id");
        return queryWrapper;
    }

    /**
     * 转换为响应VO
     */
    private DepartmentRespVO convertToRespVO(DepartmentDO department) {
        DepartmentRespVO respVO = new DepartmentRespVO();
        respVO.setId(department.getId());
        respVO.setParentId(department.getParentId());
        respVO.setDeptName(department.getDeptName());
        respVO.setDeptCode(department.getDeptCode());
        respVO.setDeptLevel(department.getDeptLevel());
        respVO.setDeptLevelDesc(getDeptLevelDesc(department.getDeptLevel()));
        respVO.setDeptType(department.getDeptType());
        respVO.setDeptTypeDesc(getDeptTypeDesc(department.getDeptType()));
        respVO.setSortOrder(department.getSortOrder());
        respVO.setStatus(department.getStatus());
        respVO.setStatusDesc(department.getStatus() == 1 ? "启用" : "禁用");
        respVO.setDescription(department.getDescription());
        respVO.setCreateTime(department.getCreateTime());
        
        // 查询用户数量
        Long userCount = userDeptRelationMapper.selectCount(
            new QueryWrapper<UserDeptRelationDO>()
                .eq("dept_id", department.getId())
                .eq("status", 1)
                .eq("deleted", 0)
        );
        respVO.setUserCount(userCount.intValue());
        
        return respVO;
    }

    /**
     * 获取部门层级描述
     */
    private String getDeptLevelDesc(Integer deptLevel) {
        if (deptLevel == null) {
            return "";
        }
        RoleTypeEnum roleType = RoleTypeEnum.getByLevel(deptLevel);
        return roleType.getDescription() + "部门";
    }

    /**
     * 获取部门类型描述
     */
    private String getDeptTypeDesc(String deptType) {
        if (deptType == null) {
            return "未分类";
        }
        switch (deptType) {
            case "OPERATION":
                return "运营部门";
            case "OPS":
                return "运维部门";
            default:
                return "未分类";
        }
    }

    /**
     * 构建部门树形结构
     */
    private List<DepartmentRespVO> buildDepartmentTree(List<DepartmentRespVO> departments) {
        if (CollectionUtils.isEmpty(departments)) {
            return Collections.emptyList();
        }

        Map<Long, List<DepartmentRespVO>> parentIdToChildren = departments.stream()
                .filter(dept -> dept.getParentId() != null && dept.getParentId() != 0)
                .collect(Collectors.groupingBy(DepartmentRespVO::getParentId));

        departments.forEach(dept -> {
            List<DepartmentRespVO> children = parentIdToChildren.get(dept.getId());
            dept.setChildren(children != null ? children : Collections.emptyList());
        });

        return departments.stream()
                .filter(dept -> dept.getParentId() == null || dept.getParentId() == 0)
                .collect(Collectors.toList());
    }

    /**
     * 校验父部门是否存在
     */
    private void validateParentDepartment(Long parentId) {
        if (parentId != null && parentId != 0) {
            validateDepartmentExists(parentId);
        }
    }

    /**
     * 校验是否有子部门
     */
    private void validateNoChildDepartments(Long id) {
        Long count = departmentMapper.selectCount(
            new QueryWrapper<DepartmentDO>()
                .eq("parent_id", id)
                .eq("status", 1)
        );
        if (count > 0) {
            throw ServiceExceptionUtil.exception(DEPARTMENT_EXITS_CHILDREN);
        }
    }

    /**
     * 校验是否有用户关联
     */
    private void validateNoUserRelations(Long id) {
        Long count = userDeptRelationMapper.selectCount(
            new QueryWrapper<UserDeptRelationDO>()
                .eq("dept_id", id)
                .eq("status", 1)
                .eq("deleted", 0)
        );
        if (count > 0) {
            throw ServiceExceptionUtil.exception(DEPARTMENT_EXITS_USERS);
        }
    }

    /**
     * 校验用户是否已在其他部门中
     */
    private void validateUserNotInOtherDepartments(Long userId, List<Long> newDeptIds, AdminUserRespDTO user) {
        if (userId == null || CollectionUtils.isEmpty(newDeptIds)) {
            return;
        }

        // 查询用户当前关联的部门
        List<UserDeptRelationDO> currentRelations = userDeptRelationMapper.selectList(
            new QueryWrapper<UserDeptRelationDO>()
                .eq("user_id", userId)
                .eq("status", 1)
                .eq("deleted", 0)
        );

        if (!CollectionUtils.isEmpty(currentRelations)) {
            // 获取当前部门信息
            List<Long> currentDeptIds = currentRelations.stream()
                .map(UserDeptRelationDO::getDeptId)
                .collect(Collectors.toList());

            // 检查是否要分配到不同的部门（如果完全相同则跳过）
            if (!currentDeptIds.equals(newDeptIds)) {
                // 获取当前部门名称用于日志记录
                List<DepartmentDO> currentDepts = departmentMapper.selectBatchIds(currentDeptIds);
                if (!CollectionUtils.isEmpty(currentDepts)) {
                    String currentDeptNames = currentDepts.stream()
                        .map(DepartmentDO::getDeptName)
                        .collect(Collectors.joining("、"));
                    
                    // 获取新部门名称
                    List<DepartmentDO> newDepts = departmentMapper.selectBatchIds(newDeptIds);
                    String newDeptNames = newDepts.stream()
                        .map(DepartmentDO::getDeptName)
                        .collect(Collectors.joining("、"));
                    
                    // 使用传入的用户信息
                    String userName = user != null ? user.getNickname() : "用户" + userId;
                    
                    // 记录部门转移日志，但不阻止操作
                    log.info("用户部门转移：用户 [{}] 从部门 [{}] 转移到部门 [{}]", 
                        userName, currentDeptNames, newDeptNames);
                }
            }
        }
    }

    /**
     * 转换为部门用户关系VO
     */
    private UserDeptRelationVO convertToUserDeptRelationVO(UserDeptRelationDO relation) {
        UserDeptRelationVO vo = new UserDeptRelationVO();
        vo.setId(relation.getId());
        vo.setUserId(relation.getUserId());
        vo.setDeptId(relation.getDeptId());
        vo.setRelationType(relation.getRelationType());
        vo.setStatus(relation.getStatus());
        vo.setCreateTime(relation.getCreateTime());
        return vo;
    }
} 