package com.mandong.api.module.erp.service.game;

import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.google.api.client.json.gson.GsonFactory;
import com.mandong.api.module.erp.dal.dataobject.site.SitePayLoginConfig;
import com.mandong.api.module.erp.dal.dataobject.user.SdkLogsUserLoginDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkOpenUser;
import com.mandong.api.module.erp.dal.mysql.site.SitePayLoginConfigMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkOpenUserMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserLoginLogsMapper;
import lombok.extern.slf4j.Slf4j;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.config.GoogleOAuthConfig;
import com.mandong.api.module.erp.controller.admin.game.vo.*;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition;
import com.mandong.api.module.erp.controller.admin.user.vo.UserDetailRespVO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkAdPageDO;
import com.mandong.api.module.erp.dal.dataobject.game.SdkShortHostsDO;
import com.mandong.api.module.erp.dal.dataobject.product.SdkProductDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkUserDO;
import com.mandong.api.module.erp.dal.sdkMysql.game.*;
import com.mandong.api.module.erp.dal.sdkMysql.product.SdkProductMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserMapper;
import com.mandong.api.module.erp.service.order.OrderServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static com.mandong.api.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.LOGIN_FAILED;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.USER_ROLE_NOT_EXISTS;

@Service
@Slf4j
public class GameServiceImpl implements GameService {

    @Resource
    private SdkProductMapper sdkProductMapper;

    @Resource
    private SdkPackGameApkMapper sdkPackGameApkMapper;

    @Resource
    private SdkPackTaskMapper sdkPackTaskMapper;

    @Resource
    private OrderServiceImpl orderService;

    @Resource
    private SdkAdPageMapper sdkAdPageMapper;

    @Resource
    private SdkShortHostsMapper sdkShortHostsMapper;

    @Resource
    private SdkUserMapper sdkUserMapper;

    @Resource
    private GoogleOAuthConfig googleOAuthConfig;

    @Resource
    private SdkOpenUserMapper sdkOpenUserMapper;

    @Resource
    private SdkUserLoginLogsMapper sdkUserLoginLogsMapper;

    @Resource
    private SitePayLoginConfigMapper sitePayLoginConfigMapper;



    @Override
    public PageResult<GamePageRespVO> getPage(GamePageReqVO pageReqVO) {
        return sdkProductMapper.selectPage(pageReqVO);
    }

    @Override
    public GameGetUrlRespVO getUrl(Integer id) {
        return null;
    }

    @Override
    public List<GameVersionRespVO> getGameVersion(Integer id) {

        return sdkPackGameApkMapper.selectGameVersion(id);
    }

    @Override
    public PageResult<GameVersionRespVO> getTaskPage(GameTaskListReqVO reqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String roleId = SecurityFrameworkUtils.getLoginUserRoleId();
        if (roleId == null) {
            throw exception(USER_ROLE_NOT_EXISTS);
        }
        List<SdkOperationCondition> operationPermittedConditions = null;
        // 如果是运营人员
        if (roleId.equals("163")) {
            // 获取用户权限范围内的产品和渠道条件
            operationPermittedConditions = orderService.getOperationUserPermittedConditions(userId);
            if (CollectionUtils.isEmpty(operationPermittedConditions)) {
                return new PageResult<>(new ArrayList<>(),0L);
            }
        }

        return sdkPackTaskMapper.getTaskPage(reqVO,operationPermittedConditions);
    }

    @Override
    public List<SdkShortHostsDO> getShortHost() {
        return sdkShortHostsMapper.selectList();
    }



    @Override
    public List<SdkAdPageDO> getAdPage(Integer id) {


        return sdkAdPageMapper.getAdPage(id);
    }

    @Override
    public UserDetailRespVO login(GameLoginReqVO reqVO) {

        String password = reqVO.getPassword();
        String username = reqVO.getUsername();
        String productId = reqVO.getProductId();

        String[] productIds = productId.split(",");


        SdkUserDO sdkUserDO = sdkUserMapper.selectOne(new MPJLambdaWrapperX<SdkUserDO>().eq(SdkUserDO::getUsername, username)
                .in(SdkUserDO::getProductId, Arrays.stream(productIds).toArray()));
        if (sdkUserDO == null) {
            throw exception(LOGIN_FAILED);
        }

        MD5 md5 = new MD5();
        String sPassword = md5.digestHex(password);
        String key = sPassword + sdkUserDO.getSlat();

        SdkUserDO userDO = sdkUserMapper.selectOne(SdkUserDO::getUsername, username, SdkUserDO::getPassword, md5.digestHex(key));
        if (userDO == null) {
            throw exception(LOGIN_FAILED);
        }

        SdkProductDO sdkProductDO = sdkProductMapper.selectOne(SdkProductDO::getId, userDO.getProductId());
        UserDetailRespVO userDetailRespVO = BeanUtils.toBean(userDO, UserDetailRespVO.class);
        userDetailRespVO.setCallbackUrl(sdkProductDO.getCallbackUrl()).setProductCode(sdkProductDO.getProductCode()).setCallbackKey(sdkProductDO.getCallbackKey());


        return userDetailRespVO;
    }

    @Override
    public UserDetailRespVO google_login(GameGoogleReqVO reqVO) {
        try {
            // 1. 验证并解析 ID Token
            GsonFactory jsonFactory = GsonFactory.getDefaultInstance();
            GoogleIdToken idToken = GoogleIdToken.parse(jsonFactory, reqVO.getAccess_token());

            if (idToken == null) {
                log.error("Google登录失败：无效的ID Token");
                throw exception(LOGIN_FAILED);
            }

            // 2. 验证 ID Token 的签发者和受众
            GoogleIdToken.Payload payload = idToken.getPayload();

            SitePayLoginConfig sitePayLoginConfig = sitePayLoginConfigMapper.selectOne(SitePayLoginConfig::getSitePayId, reqVO.getSiteId(), SitePayLoginConfig::getType, 1);

            if (sitePayLoginConfig == null) {
                throw exception(LOGIN_FAILED);
            }

            // 验证受众（客户端ID）
            if (!sitePayLoginConfig.getClientId().equals(payload.getAudience())) {
                log.error("Google登录失败：ID Token 受众验证失败，期望: {}, 实际: {}",
                        sitePayLoginConfig.getClientId(), payload.getAudience());
                throw exception(LOGIN_FAILED);
            }

            // 验证签发者
            String issuer = payload.getIssuer();
            if (!"https://accounts.google.com".equals(issuer) && !"accounts.google.com".equals(issuer)) {
                log.error("Google登录失败：ID Token 签发者验证失败: {}", issuer);
                throw exception(LOGIN_FAILED);
            }

            // 验证过期时间
            if (payload.getExpirationTimeSeconds() != null &&
                payload.getExpirationTimeSeconds() < System.currentTimeMillis() / 1000) {
                log.error("Google登录失败：ID Token 已过期");
                throw exception(LOGIN_FAILED);
            }

            // 3. 提取用户信息
            String email = payload.getEmail();
            String googleUserId = payload.getSubject();
            String name = (String) payload.get("name");

            log.info("Google登录用户信息 - Email: {}, GoogleID: {}, Name: {}", email, googleUserId, name);

            // 4. 根据Google用户信息查找系统中的用户
            // 优先使用email查找，其次使用Google ID
            SdkUserDO sdkUserDO = findUserByGoogleInfo(name, googleUserId, reqVO.getProductId());

            if (sdkUserDO == null) {
                log.error("Google登录失败：用户不存在 - Email: {}, GoogleID: {}", email, googleUserId);
                throw exception(LOGIN_FAILED);
            }

            // 5. 获取产品信息
            SdkProductDO sdkProductDO = sdkProductMapper.selectOne(SdkProductDO::getId, sdkUserDO.getProductId());

            // 6. 构建返回结果
            UserDetailRespVO userDetailRespVO = BeanUtils.toBean(sdkUserDO, UserDetailRespVO.class);
            userDetailRespVO.setCallbackUrl(sdkProductDO.getCallbackUrl())
                           .setProductCode(sdkProductDO.getProductCode())
                           .setCallbackKey(sdkProductDO.getCallbackKey());

            log.info("Google登录成功 - 用户ID: {}, 用户名: {}", sdkUserDO.getUid(), sdkUserDO.getUsername());
            return userDetailRespVO;

        } catch (Exception e) {
            // 记录详细错误日志
            log.error("Google登录失败，ID Token: {}, 错误信息: {}",
                     reqVO.getAccess_token() != null ? reqVO.getAccess_token().substring(0, Math.min(50, reqVO.getAccess_token().length())) + "..." : "null",
                     e.getMessage(), e);
            throw exception(LOGIN_FAILED);
        }
    }

    /**
     * 根据Google用户信息查找系统用户
     * @param name Google用户
     * @param googleUserId Google用户ID
     * @param productIds 产品ID数组
     * @return 用户信息
     */
    private SdkUserDO findUserByGoogleInfo(String name, String googleUserId, String productIds) {
        SdkUserDO user = null;
        String[] split = productIds.split(",");
        SdkOpenUser sdkOpenUser = sdkOpenUserMapper.selectOne(new MPJLambdaWrapperX<SdkOpenUser>().eq(SdkOpenUser::getUseropenid, googleUserId).eq(SdkOpenUser::getOtheraccountname, name));
        user = sdkUserMapper.selectOne(new MPJLambdaWrapperX<SdkUserDO>().eq(SdkUserDO::getUid, sdkOpenUser.getUid()));
        List<SdkLogsUserLoginDO> sdkLogsUserLoginDO = sdkUserLoginLogsMapper.
                selectList(new MPJLambdaWrapperX<SdkLogsUserLoginDO>().eq(SdkLogsUserLoginDO::getUid, user.getUid()).orderByDesc(SdkLogsUserLoginDO::getCreateTime));
        Optional<SdkLogsUserLoginDO> first = sdkLogsUserLoginDO.stream().findFirst();
        if (first.isPresent()) {
            user.setProductId(Long.valueOf(first.get().getProductId()));
        }

        return user;
    }


}
