package com.mandong.api.module.erp.service.leadGroup;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.leadGroup.vo.*;
import com.mandong.api.module.erp.dal.dataobject.leadGroup.SdkLeadGroupLinkDO;
import com.mandong.api.module.erp.dto.permission.OrderPermissionDTO;
import jakarta.validation.Valid;

import java.util.List;

public interface SdkLeadGroupService {
    PageResult<LeadGroupRespVO> getLeadGroupPage(LeadGroupPageReqVO pageReqVO);
    PageResult<LeadGroupLinksPageRespVO> getLeadGroupLinksPage(LeadGroupLinksPageReqVO pageReqVO);

    /**
     * 带权限过滤的分页查询
     */
    PageResult<LeadGroupRespVO> getLeadGroupPageWithPermission(LeadGroupPageReqVO pageReqVO, OrderPermissionDTO permission);
    
    /**
     * 带权限过滤的关联分页查询
     */
    PageResult<LeadGroupLinksPageRespVO> getLeadGroupLinksPageWithPermission(LeadGroupLinksPageReqVO pageReqVO, OrderPermissionDTO permission);

    int delete(long id);
    int deleteGroupLinks(long id);
    int add(LeadGroupAddReqVO reqVO);
    int createGroupLinks(LeadGroupLinkAddReqVO reqVO);

    List<SdkLeadGroupLinkDO> getLeadGroupLinks(long id);

    List<LeadGroupMonthlyStatsVO> getLeadGroupMonthlyStats(Long id ,Long  startTime,Long  endTime);
}
