package com.mandong.api.module.erp.service.leadGroup;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.controller.admin.leadGroup.vo.*;
import com.mandong.api.module.erp.dal.dataobject.leadGroup.SdkLeadGroupDO;
import com.mandong.api.module.erp.dal.dataobject.leadGroup.SdkLeadGroupLinkDO;
import com.mandong.api.module.erp.dal.sdkMysql.leaderGroup.SdkLeadGroupLinkMapper;
import com.mandong.api.module.erp.dal.sdkMysql.leaderGroup.SdkLeadGroupMapper;
import com.mandong.api.module.erp.dto.permission.OrderPermissionDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.springframework.util.CollectionUtils;

import static com.mandong.api.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.*;
import static com.mandong.api.module.system.enums.ErrorCodeConstants.USER_USERNAME_EXISTS;

@Slf4j
@Service
@Validated
@DS("sdkDB")
public class SdkLeadGroupServiceImpl implements SdkLeadGroupService{
    @Resource
    SdkLeadGroupMapper sdkLeadGroupMapper;

    @Resource
    SdkLeadGroupLinkMapper sdkLeadGroupLinkMapper;

    @Override
    public PageResult<LeadGroupRespVO> getLeadGroupPage(LeadGroupPageReqVO pageReqVO) {
        List<String> adminRole = Arrays.asList("1","2","3","4");
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String roleId = SecurityFrameworkUtils.getLoginUserRoleId();
        if (roleId == null) {
            throw exception(USER_ROLE_NOT_EXISTS);
        }

        // 如果不是管理员角色 ，查询他自己
        if(!adminRole.contains(roleId)){
            pageReqVO.setUid(userId);
        }

        return sdkLeadGroupMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<LeadGroupRespVO> getLeadGroupPageWithPermission(LeadGroupPageReqVO pageReqVO, OrderPermissionDTO permission) {
        // 如果是管理员，直接查询所有数据
        if (permission != null && permission.getHasAllPermission() != null && permission.getHasAllPermission()) {
            return getLeadGroupPage(pageReqVO);
        }
        
        // 根据权限过滤用户ID
        if (permission != null && permission.getAccessibleUserIds() != null && !permission.getAccessibleUserIds().isEmpty()) {
            // 如果有权限控制，只查询可访问的用户
            if (pageReqVO.getUid() == null) {
                // 如果没有指定用户ID，则限制在权限范围内
                // 这里需要根据实际业务逻辑来实现权限过滤
                // 可以在查询参数中添加用户ID列表过滤
            }
        }
        
        return getLeadGroupPage(pageReqVO);
    }

    @Override
    public PageResult<LeadGroupLinksPageRespVO> getLeadGroupLinksPage(LeadGroupLinksPageReqVO pageReqVO) {
        return sdkLeadGroupLinkMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<LeadGroupLinksPageRespVO> getLeadGroupLinksPageWithPermission(LeadGroupLinksPageReqVO pageReqVO, OrderPermissionDTO permission) {
        // 如果是管理员，直接查询所有数据
        if (permission != null && permission.getHasAllPermission() != null && permission.getHasAllPermission()) {
            return sdkLeadGroupLinkMapper.selectPage(pageReqVO);
        }
        
        // 创建带权限过滤的查询条件
        LeadGroupLinksPageReqVO filteredReqVO = new LeadGroupLinksPageReqVO();
        // 复制原始查询条件
        filteredReqVO.setPageNo(pageReqVO.getPageNo());
        filteredReqVO.setPageSize(pageReqVO.getPageSize());
        filteredReqVO.setLinkId(pageReqVO.getLinkId());
        filteredReqVO.setProductId(pageReqVO.getProductId());
        filteredReqVO.setServerName(pageReqVO.getServerName());
        filteredReqVO.setCreateTime(pageReqVO.getCreateTime());

        // 添加权限过滤条件
        if (permission != null) {
            if (!CollectionUtils.isEmpty(permission.getAccessibleProductIds())) {
                // 如果原查询指定了产品ID，需要与权限范围取交集
                if (!CollectionUtils.isEmpty(pageReqVO.getProductId())) {
                    List<Long> filteredProductIds = pageReqVO.getProductId().stream()
                        .filter(id -> permission.getAccessibleProductIds().contains(id))
                        .collect(java.util.stream.Collectors.toList());
                    filteredReqVO.setProductId(filteredProductIds);
                } else {
                    filteredReqVO.setProductId(permission.getAccessibleProductIds());
                }
            }
        }

        return sdkLeadGroupLinkMapper.selectPage(filteredReqVO);
    }

    @Override
    public List<SdkLeadGroupLinkDO> getLeadGroupLinks(long id) {

        return sdkLeadGroupLinkMapper.selectList(SdkLeadGroupLinkDO::getLinkId, id);
    }

    @Override
    public List<LeadGroupMonthlyStatsVO> getLeadGroupMonthlyStats(Long id,Long  stime,Long  etime) {
        // 获取当前日期
        LocalDate now = LocalDate.now();

        // 计算上个月和上上个月的日期
        LocalDate twoMonthsAgo = now.minusMonths(2);

        // 计算开始时间和结束时间
        LocalDateTime startOfMonthTwoMonthsAgo = LocalDateTime.of(twoMonthsAgo.withDayOfMonth(1), LocalTime.MIN);
        LocalDateTime endOfDayNow = LocalDateTime.of(now, LocalTime.MAX);

        // 将LocalDateTime转换为Instant以获取时间戳
        Instant startInstantTwoMonthsAgo = startOfMonthTwoMonthsAgo.atZone(ZoneId.systemDefault()).toInstant();
        Instant endInstantNow = endOfDayNow.atZone(ZoneId.systemDefault()).toInstant();

        // 转换为秒级时间戳
        long startTime = startInstantTwoMonthsAgo.getEpochSecond();
        long endTime = endInstantNow.getEpochSecond();
        if (stime!=null) {
            startTime =  stime;
            endTime =  etime;
        }
        List<LeadGroupOptVO> sdkLeadGroupLinkDOS = sdkLeadGroupLinkMapper.selectListByOpt(id);



        return sdkLeadGroupLinkMapper.getLeadGroupMonthlyStats(id,startTime,endTime,sdkLeadGroupLinkDOS);
    }

    @Override
    public int delete(long id) {

        return sdkLeadGroupMapper.delete(SdkLeadGroupDO::getId,id);
    }

    @Override
    public int deleteGroupLinks(long id) {
        return sdkLeadGroupLinkMapper.delete(SdkLeadGroupLinkDO::getId,id);
    }

    @Override
    public int add(LeadGroupAddReqVO reqVO) {
        SdkLeadGroupDO bean = BeanUtils.toBean(reqVO, SdkLeadGroupDO.class);


        return  sdkLeadGroupMapper.insert(bean);
    }

    @Override
    public int createGroupLinks(LeadGroupLinkAddReqVO reqVO) {



        SdkLeadGroupDO sdkLeadGroupDO = sdkLeadGroupMapper.selectById(reqVO.getLinkId());
        if (sdkLeadGroupDO == null) {
            throw exception(LEAD_GROUP_NOT_EXISTS);
        }
        List<SdkLeadGroupLinkDO> beans = new ArrayList<>();
        List<LeadGroupLinkAddReqVO.ProductVO> productList = reqVO.getProductList();
        productList.forEach(p -> {
            SdkLeadGroupLinkDO sdkLeadGroupLinkDO = sdkLeadGroupLinkMapper.selectOne(SdkLeadGroupLinkDO::getProductId, p.getProductId(), SdkLeadGroupLinkDO::getServerName, reqVO.getServerName(), SdkLeadGroupLinkDO::getOptGroupName, reqVO.getOptGroupName());
            if (sdkLeadGroupLinkDO != null) {
                throw exception(LEAD_GROUP_LINKS_EXISTS_SERVER,sdkLeadGroupLinkDO.getProductName(),sdkLeadGroupLinkDO.getServerName(),sdkLeadGroupLinkDO.getOptGroupName());
            }
            SdkLeadGroupLinkDO leadGroupLinkDO = new SdkLeadGroupLinkDO().setProductId(p.getProductId())
                    .setLinkId(reqVO.getLinkId())
                    .setProductName(p.getProductName())
                    .setOptGroupName(reqVO.getOptGroupName())
                    .setServerName(reqVO.getServerName());
            beans.add(leadGroupLinkDO);
        });


        if (!beans.isEmpty()) {
            sdkLeadGroupLinkMapper.insertBatch(beans);
        }

        return 0;
    }
}
