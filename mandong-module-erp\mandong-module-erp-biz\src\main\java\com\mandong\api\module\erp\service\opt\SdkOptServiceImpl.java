package com.mandong.api.module.erp.service.opt;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.util.object.BeanUtils;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.controller.admin.opt.vo.*;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptDO;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptLinkDO;
import com.mandong.api.module.erp.dal.sdkMysql.opt.SdkOptLinkMapper;
import com.mandong.api.module.erp.dal.sdkMysql.opt.SdkOptMapper;
import com.mandong.api.module.erp.dto.permission.OrderPermissionDTO;
import com.mandong.api.module.erp.enums.RoleTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.time.*;
import java.util.Arrays;
import java.util.List;

import static com.mandong.api.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.*;

@Slf4j
@Service
@Validated
@DS("sdkDB")
public class SdkOptServiceImpl implements SdkOptService {
    @Resource
    SdkOptMapper sdkOptMapper;

    @Resource
    SdkOptLinkMapper sdkOptLinkMapper;

    @Override
    public PageResult<OptRespVO> getOptPage(OptPageReqVO pageReqVO) {
        List<String> adminRole = Arrays.asList("1","2","3","4");
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String roleId = SecurityFrameworkUtils.getLoginUserRoleId();
        if (roleId == null) {
            throw exception(USER_ROLE_NOT_EXISTS);
        }

        // 如果不是管理员角色 ，查询他自己
        if(!adminRole.contains(roleId)){
            pageReqVO.setUid(userId);
        }

        return sdkOptMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<OptRespVO> getOptPageWithPermission(OptPageReqVO pageReqVO, OrderPermissionDTO permission) {
        // 如果是管理员，直接查询所有数据
        if (permission != null && permission.getHasAllPermission() != null && permission.getHasAllPermission()) {
            return sdkOptMapper.selectPage(pageReqVO);
        }
        
        if (permission == null) {
            return new PageResult<>();
        }

        // 创建带权限过滤的查询条件
        OptPageReqVO filteredReqVO = new OptPageReqVO();
        // 复制原始查询条件
        filteredReqVO.setPageNo(pageReqVO.getPageNo());
        filteredReqVO.setPageSize(pageReqVO.getPageSize());
        filteredReqVO.setGroupName(pageReqVO.getGroupName());
        filteredReqVO.setUserName(pageReqVO.getUserName());
        filteredReqVO.setUid(pageReqVO.getUid());
        filteredReqVO.setCreateTime(pageReqVO.getCreateTime());

        // 根据权限级别过滤
        if (permission.getRoleType() == RoleTypeEnum.TEAM_MEMBER) {
            // 组员只能查看自己的数据
            filteredReqVO.setUid(permission.getUserId());
        } else if (permission.getRoleType() == RoleTypeEnum.TEAM_LEADER) {
            // 组长可以查看自己组的数据
            if (!CollectionUtils.isEmpty(permission.getAccessibleUserIds())) {
                // 如果指定了用户ID，需要与权限范围取交集
                if (pageReqVO.getUid() != null) {
                    if (permission.getAccessibleUserIds().contains(pageReqVO.getUid())) {
                        filteredReqVO.setUid(pageReqVO.getUid());
                    } else {
                        // 用户没有权限访问指定用户的数据，返回空结果
                        return new PageResult<>();
                    }
                }
                // 如果没有指定用户ID，可以查看所有权限范围内的数据
                // 这里需要根据实际的Mapper实现来决定如何过滤
                // 暂时不做额外处理，让SQL查询处理
            }
        }
        // 主管可以查看所有下属数据，不需要额外过滤

        return sdkOptMapper.selectPage(filteredReqVO);
    }

    @Override
    public PageResult<OptLinksPageRespVO> getOptLinksPage(OptLinksPageReqVO pageReqVO) {
        return sdkOptLinkMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<OptLinksPageRespVO> getOptLinksPageWithPermission(OptLinksPageReqVO pageReqVO, OrderPermissionDTO permission) {
        // 如果是管理员，直接查询所有数据
        if (permission != null && permission.getHasAllPermission() != null && permission.getHasAllPermission()) {
            return sdkOptLinkMapper.selectPage(pageReqVO);
        }
        
        if (permission == null) {
            return new PageResult<>();
        }

        // 创建带权限过滤的查询条件
        OptLinksPageReqVO filteredReqVO = new OptLinksPageReqVO();
        // 复制原始查询条件
        filteredReqVO.setPageNo(pageReqVO.getPageNo());
        filteredReqVO.setPageSize(pageReqVO.getPageSize());
        filteredReqVO.setLinkId(pageReqVO.getLinkId());
        filteredReqVO.setProductId(pageReqVO.getProductId());
        filteredReqVO.setChannelCode(pageReqVO.getChannelCode());
        filteredReqVO.setServerName(pageReqVO.getServerName());
        filteredReqVO.setCreateTime(pageReqVO.getCreateTime());

        // 添加权限过滤条件
        if (!CollectionUtils.isEmpty(permission.getAccessibleProductIds())) {
            // 如果原查询指定了产品ID，需要与权限范围取交集
            if (!CollectionUtils.isEmpty(pageReqVO.getProductId())) {
                List<Long> filteredProductIds = pageReqVO.getProductId().stream()
                    .filter(id -> permission.getAccessibleProductIds().contains(id))
                    .collect(java.util.stream.Collectors.toList());
                filteredReqVO.setProductId(filteredProductIds);
            } else {
                filteredReqVO.setProductId(permission.getAccessibleProductIds());
            }
        }
        
        if (!CollectionUtils.isEmpty(permission.getAccessibleChannels())) {
            // 如果原查询指定了渠道，需要与权限范围取交集
            if (pageReqVO.getChannelCode() != null) {
                if (!permission.getAccessibleChannels().contains(pageReqVO.getChannelCode())) {
                    // 用户没有权限访问指定渠道，返回空结果
                    return new PageResult<>();
                }
            }
            // 如果没有指定渠道，可以查看所有权限范围内的渠道
            // 这里需要根据实际的Mapper实现来决定如何过滤
            // 暂时不做额外处理，让SQL查询处理
        }

        return sdkOptLinkMapper.selectPage(filteredReqVO);
    }

    @Override
    public List<SdkOptLinkDO> getOptLinks(long id) {
        return sdkOptLinkMapper.selectList(SdkOptLinkDO::getLinkId, id);
    }

    @Override
    public List<OptMonthlyStatsVO> getOptMonthlyStats(Long id,Long  stime,Long  etime) {
        // 获取当前日期
        LocalDate now = LocalDate.now();

        // 计算上个月和上上个月的日期
        LocalDate twoMonthsAgo = now.minusMonths(2);

        // 计算开始时间和结束时间
        LocalDateTime startOfMonthTwoMonthsAgo = LocalDateTime.of(twoMonthsAgo.withDayOfMonth(1), LocalTime.MIN);
        LocalDateTime endOfDayNow = LocalDateTime.of(now, LocalTime.MAX);

        // 将LocalDateTime转换为Instant以获取时间戳
        Instant startInstantTwoMonthsAgo = startOfMonthTwoMonthsAgo.atZone(ZoneId.systemDefault()).toInstant();
        Instant endInstantNow = endOfDayNow.atZone(ZoneId.systemDefault()).toInstant();

        // 转换为秒级时间戳
        long startTime = startInstantTwoMonthsAgo.getEpochSecond();
        long endTime = endInstantNow.getEpochSecond();

        if (stime != null) {
            startTime = stime;
            endTime = etime;
        }
        return sdkOptLinkMapper.getOptMonthlyStats(id,startTime,endTime);
    }

    @Override
    public int delete(long id) {
        return sdkOptMapper.delete(SdkOptDO::getId,id);
    }

    @Override
    public int deleteGroupLinks(long id) {
        return sdkOptLinkMapper.delete(SdkOptLinkDO::getId,id);
    }

    @Override
    public int add(OptAddReqVO reqVO) {
        SdkOptDO bean = BeanUtils.toBean(reqVO, SdkOptDO.class);
        return  sdkOptMapper.insert(bean);
    }

    @Override
    public int createGroupLinks(OptLinkAddReqVO reqVO) {
        SdkOptDO sdkOptDO = sdkOptMapper.selectById(reqVO.getLinkId());
        if (sdkOptDO == null) {
            throw exception(OPT_NOT_EXISTS);
        }
        SdkOptLinkDO sdkOptLinkDO = sdkOptLinkMapper.selectOne(SdkOptLinkDO::getProductId, reqVO.getProductId(), SdkOptLinkDO::getChannelCode, reqVO.getChannelCode());
        if (sdkOptLinkDO != null) {
            throw exception(OPT_LINKS_EXISTS_SERVER);
        }

        return sdkOptLinkMapper.insert(BeanUtils.toBean(reqVO, SdkOptLinkDO.class));
    }
}
