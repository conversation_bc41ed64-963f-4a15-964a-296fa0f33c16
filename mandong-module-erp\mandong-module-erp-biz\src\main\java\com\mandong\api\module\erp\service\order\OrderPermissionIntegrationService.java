package com.mandong.api.module.erp.service.order;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.module.erp.controller.admin.order.vo.OrderPageReqVO;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderRespVo;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderTotalAmountRespVo;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryReqVO;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryRespVO;

import java.util.List;

/**
 * 订单权限集成服务
 */
public interface OrderPermissionIntegrationService {
    
    /**
     * 带权限控制的订单分页查询
     */
    PageResult<SdkOrderRespVo> getOrderPageWithPermission(Long userId, OrderPageReqVO pageReqVO);
    
    /**
     * 带权限控制的订单金额统计
     */
    SdkOrderTotalAmountRespVo getOrderPageTotalAmountWithPermission(Long userId, OrderPageReqVO pageReqVO);
    
    /**
     * 带权限控制的订单支付统计
     */
    SdkOrderSummaryRespVO getOrderPaySummaryWithPermission(Long userId, SdkOrderSummaryReqVO reqVO);
    
    /**
     * 检查用户是否有权限查看指定订单
     */
    boolean canViewOrder(Long userId, Long orderId);
    
    /**
     * 为查询添加权限过滤条件
     */
    OrderPageReqVO addPermissionFilter(OrderPageReqVO pageReqVO, List<Long> userId);
} 