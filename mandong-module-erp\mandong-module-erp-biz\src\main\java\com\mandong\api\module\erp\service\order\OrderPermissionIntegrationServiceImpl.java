package com.mandong.api.module.erp.service.order;

import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.controller.admin.order.vo.OrderPageReqVO;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderRespVo;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderTotalAmountRespVo;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryReqVO;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryRespVO;
import com.mandong.api.module.erp.dto.permission.OrderPermissionDTO;
import com.mandong.api.module.erp.service.permission.DataPermissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单权限集成服务实现类
 */
@Service
@Slf4j
public class OrderPermissionIntegrationServiceImpl implements OrderPermissionIntegrationService {

    @Resource
    private OrderService orderService;
    
    @Resource
    private DataPermissionService dataPermissionService;

    @Override
    public PageResult<SdkOrderRespVo> getOrderPageWithPermission(Long userId, OrderPageReqVO pageReqVO) {
        // 1. 获取用户权限信息
        OrderPermissionDTO permission = dataPermissionService.getUserOrderPermission(userId);
        
        // 2. 如果是管理员，直接调用无权限限制的查询
        if (permission != null && permission.getHasAllPermission() != null && permission.getHasAllPermission()) {
            return orderService.getOrderPage(pageReqVO);
        }
        
        // 3. 如果用户没有任何权限，返回空结果
        if (permission == null || (CollectionUtils.isEmpty(permission.getAccessibleProductIds()) && 
            CollectionUtils.isEmpty(permission.getAccessibleChannels()) && 
            CollectionUtils.isEmpty(permission.getAccessibleServers()) && CollectionUtils.isEmpty(permission.getAccessibleUserIds()))) {
            return new PageResult<>();
        }
        
        // 4. 为查询条件添加权限过滤
        OrderPageReqVO filteredReqVO = addPermissionFilter(pageReqVO, permission.getAccessibleUserIds());
        
        // 5. 调用原有的订单查询服务
        return orderService.getOrderPage(filteredReqVO);
    }

    @Override
    public SdkOrderTotalAmountRespVo getOrderPageTotalAmountWithPermission(Long userId, OrderPageReqVO pageReqVO) {
        // 1. 获取用户权限信息
        OrderPermissionDTO permission = dataPermissionService.getUserOrderPermission(userId);
        
        // 2. 如果是管理员，直接调用无权限限制的查询
        if (permission != null && permission.getHasAllPermission() != null && permission.getHasAllPermission()) {
            return orderService.getOrderPageTotalAmount(pageReqVO);
        }
        
        // 3. 如果用户没有任何权限，返回空结果
        if (permission == null || (CollectionUtils.isEmpty(permission.getAccessibleProductIds()) && 
            CollectionUtils.isEmpty(permission.getAccessibleChannels()) && 
            CollectionUtils.isEmpty(permission.getAccessibleServers()) && CollectionUtils.isEmpty(permission.getAccessibleUserIds()))) {
            return new SdkOrderTotalAmountRespVo();
        }
        
        // 4. 为查询条件添加权限过滤
        OrderPageReqVO filteredReqVO = addPermissionFilter(pageReqVO, permission.getAccessibleUserIds());
        
        // 5. 调用原有的订单金额统计服务
        return orderService.getOrderPageTotalAmount(filteredReqVO);
    }

    @Override
    public SdkOrderSummaryRespVO getOrderPaySummaryWithPermission(Long userId, SdkOrderSummaryReqVO reqVO) {
        // 1. 获取用户权限信息
        OrderPermissionDTO permission = dataPermissionService.getUserOrderPermission(userId);
        
        // 2. 如果是管理员，直接调用无权限限制的查询
        if (permission != null && permission.getHasAllPermission() != null && permission.getHasAllPermission()) {
            return orderService.getOrderPaySummary(reqVO);
        }
        
        // 3. 如果用户没有任何权限，返回空结果
        if (permission == null || (CollectionUtils.isEmpty(permission.getAccessibleProductIds()) && 
            CollectionUtils.isEmpty(permission.getAccessibleChannels()) && 
            CollectionUtils.isEmpty(permission.getAccessibleServers())&& CollectionUtils.isEmpty(permission.getAccessibleUserIds()))) {
            return new SdkOrderSummaryRespVO();
        }
        
        // 4. 为查询条件添加权限过滤
        SdkOrderSummaryReqVO filteredReqVO = addPermissionFilterForSummary(reqVO, permission);
        
        // 5. 调用原有的订单支付统计服务
        return orderService.getOrderPaySummary(filteredReqVO);
    }

    @Override
    public boolean canViewOrder(Long userId, Long orderId) {
        // 检查管理员权限
        OrderPermissionDTO permission = dataPermissionService.getUserOrderPermission(userId);
        if (permission != null && permission.getHasAllPermission() != null && permission.getHasAllPermission()) {
            return true;
        }
        
        // 通过订单ID获取订单信息，然后检查权限
        // 这里需要根据实际的订单表结构来实现
        // 暂时先返回true，具体实现需要根据订单表结构调整
        return true;
    }

    @Override
    public OrderPageReqVO addPermissionFilter(OrderPageReqVO pageReqVO, List<Long> userIds) {

        
        // 创建新的查询条件对象，避免修改原始对象
        OrderPageReqVO filteredReqVO = new OrderPageReqVO();
        
        // 复制原始查询条件
        filteredReqVO.setPageNo(pageReqVO.getPageNo());
        filteredReqVO.setPageSize(pageReqVO.getPageSize());
        filteredReqVO.setUsername(pageReqVO.getUsername());
        filteredReqVO.setOrderNo(pageReqVO.getOrderNo());
        filteredReqVO.setPayStatus(pageReqVO.getPayStatus());
        filteredReqVO.setCreateTime(pageReqVO.getCreateTime());
        filteredReqVO.setPayTime(pageReqVO.getPayTime());
        filteredReqVO.setRoleId(pageReqVO.getRoleId());
        filteredReqVO.setRoleName(pageReqVO.getRoleName());
        filteredReqVO.setServerName(pageReqVO.getServerName());
        filteredReqVO.setPayName(pageReqVO.getPayName());
        filteredReqVO.setPayment(pageReqVO.getPayment());
        filteredReqVO.setUid(pageReqVO.getUid());
        filteredReqVO.setOrderSource(pageReqVO.getOrderSource());
        filteredReqVO.setUserIds(userIds);
        filteredReqVO.setProductId(pageReqVO.getProductId());

        // 添加权限过滤条件
        
    /*    // 产品权限过滤
        if (!CollectionUtils.isEmpty(permission.getAccessibleProductIds())) {
            filteredReqVO.setProductId(permission.getAccessibleProductIds());
        }
        
        // 渠道权限过滤
        if (!CollectionUtils.isEmpty(permission.getAccessibleChannels())) {
            filteredReqVO.setChannelCode(permission.getAccessibleChannels());
        }
        
        // 区服权限过滤
        if (!CollectionUtils.isEmpty(permission.getAccessibleServers())) {
            filteredReqVO.setServers(permission.getAccessibleServers());
        }*/
        
        return filteredReqVO;
    }

    /**
     * 为统计查询添加权限过滤条件
     */
    private SdkOrderSummaryReqVO addPermissionFilterForSummary(SdkOrderSummaryReqVO reqVO, OrderPermissionDTO permission) {

        // 如果是管理员，直接返回原始查询条件
        if (permission != null && permission.getHasAllPermission() != null && permission.getHasAllPermission()) {
            return reqVO;
        }
        
        // 创建新的查询条件对象，避免修改原始对象
        SdkOrderSummaryReqVO filteredReqVO = new SdkOrderSummaryReqVO();
        
        // 复制原始查询条件
        filteredReqVO.setProductId(reqVO.getProductId());
        filteredReqVO.setChannelCode(reqVO.getChannelCode());
        filteredReqVO.setPayTime(reqVO.getPayTime());
        filteredReqVO.setServerName(reqVO.getServerName());
        filteredReqVO.setServers(reqVO.getServers());
        if (permission != null) {
            filteredReqVO.setUserIds(permission.getAccessibleUserIds());
        }

        // 添加权限过滤条件
        
        // 产品权限过滤
        if (!CollectionUtils.isEmpty(permission.getAccessibleProductIds())) {
            // 如果原始查询已经指定了产品ID，需要与权限范围取交集
            if (!CollectionUtils.isEmpty(reqVO.getProductId())) {
                List<Long> filteredProductIds = reqVO.getProductId().stream()
                    .filter(id -> permission.getAccessibleProductIds().contains(id))
                    .toList();
                filteredReqVO.setProductId(filteredProductIds);
            } else {
                filteredReqVO.setProductId(permission.getAccessibleProductIds());
            }
        }
        
        // 渠道权限过滤
        if (!CollectionUtils.isEmpty(permission.getAccessibleChannels())) {
            // 如果原始查询已经指定了渠道，需要与权限范围取交集
            if (reqVO.getChannelCode() != null && !reqVO.getChannelCode().isEmpty()) {
                List<String> filteredChannels = reqVO.getChannelCode().stream()
                    .filter(code -> permission.getAccessibleChannels().contains(code))
                    .collect(Collectors.toList());
                filteredReqVO.setChannelCode(filteredChannels);
            } else {
                filteredReqVO.setChannelCode(permission.getAccessibleChannels());
            }
        }
        
        // 区服权限过滤
        if (!CollectionUtils.isEmpty(permission.getAccessibleServers())) {
            // 如果原始查询已经指定了区服，需要与权限范围取交集
            if (!CollectionUtils.isEmpty(reqVO.getServers())) {
                List<String> filteredServers = reqVO.getServers().stream()
                    .filter(server -> permission.getAccessibleServers().contains(server))
                    .collect(Collectors.toList());
                filteredReqVO.setServers(filteredServers);
            } else {
                filteredReqVO.setServers(permission.getAccessibleServers());
            }
        }
        
        return filteredReqVO;
    }
} 