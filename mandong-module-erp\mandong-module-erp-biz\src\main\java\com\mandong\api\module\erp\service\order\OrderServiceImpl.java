package com.mandong.api.module.erp.service.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.controller.admin.leadGroup.vo.LeadGroupOptVO;
import com.mandong.api.module.erp.controller.admin.leadGroup.vo.LeadGroupVO;
import com.mandong.api.module.erp.controller.admin.server.vo.ServerPageRespVO;
import com.mandong.api.module.erp.controller.admin.user.vo.SdkUserLiveSummaryRespVO;
import com.mandong.api.module.erp.controller.admin.user.vo.SdkUserSummaryMonthRespVO;
import com.mandong.api.module.erp.controller.admin.user.vo.SdkUserSummaryReqVO;
import com.mandong.api.module.erp.controller.admin.user.vo.SdkUserSummaryRespVO;
import com.mandong.api.module.erp.controller.admin.user.vo.SdkUserSummaryRespVO;
import com.mandong.api.module.erp.dal.dataobject.leadGroup.SdkLeadGroupLinkDO;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptLinkDO;
import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO;
import com.mandong.api.module.erp.dal.sdkMysql.leaderGroup.SdkLeadGroupLinkMapper;
import com.mandong.api.module.erp.dal.sdkMysql.leaderGroup.SdkLeadGroupMapper;
import com.mandong.api.module.erp.dal.sdkMysql.opt.SdkOptMapper;
import com.mandong.api.module.erp.dal.sdkMysql.order.SdkOrderMapper;
import com.mandong.api.module.erp.dal.sdkMysql.role.SdkRoleMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserMapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.mandong.api.module.erp.controller.admin.order.vo.*;
import com.mandong.api.module.erp.dal.dataobject.order.OrderDO;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.common.util.object.BeanUtils;

import com.mandong.api.module.erp.dal.mysql.order.OrderMapper;

import static com.mandong.api.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.mandong.api.framework.common.util.collection.CollectionUtils.convertList;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Objects;
import java.util.Collections;
import com.mandong.api.module.erp.service.permission.DataPermissionService;

/**
 * 订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OrderServiceImpl implements OrderService {

    @Resource
    private OrderMapper orderMapper;
    @Resource
    private SdkOrderMapper sdkOrderMapper;
    @Resource
    private SdkUserMapper sdkUserMapper;
   @Resource
    private SdkRoleMapper sdkRoleMapper;

    @Resource
    private SdkLeadGroupMapper sdkLeadGroupMapper;
    @Resource
    private SdkLeadGroupLinkMapper sdkLeadGroupLinkMapper;

    @Resource
    private SdkOptMapper sdkOptMapper;

    @Resource  
    private DataPermissionService dataPermissionService;


    @Override
    public Integer createOrder(OrderSaveReqVO createReqVO) {
        // 插入
        OrderDO order = BeanUtils.toBean(createReqVO, OrderDO.class);
        orderMapper.insert(order);
        // 返回
        return order.getId();
    }

    @Override
    public void updateOrder(OrderSaveReqVO updateReqVO) {

    }


    @Override
    public void deleteOrder(Integer id) {
        // 校验存在
        validateOrderExists(id);
        // 删除
        orderMapper.deleteById(id);
    }

    private void validateOrderExists(Integer id) {
        if (orderMapper.selectById(id) == null) {
            throw exception(ORDER_NOT_EXISTS);
        }
    }

    @Override
    public OrderDO getOrder(Integer id) {
        return orderMapper.selectById(id);
    }



    @Override
    public PageResult<SdkOrderRespVo> getOrderPage(OrderPageReqVO pageReqVO) {
        return sdkGetOrderPage(pageReqVO);

    }

    @Master
    public PageResult<OrderDO> lcGetOrderPage(OrderPageReqVO pageReqVO){
        return orderMapper.selectPage(pageReqVO);
    }
    @DS("sdkDB")
    public PageResult<SdkOrderRespVo> sdkGetOrderPage(OrderPageReqVO pageReqVO){
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String roleId = SecurityFrameworkUtils.getLoginUserRoleId();
        if (roleId == null) {
            throw exception(USER_ROLE_NOT_EXISTS);
        }

        // 使用新的权限系统生成权限SQL
        String permissionSql = dataPermissionService.generatePermissionSql(userId, "t",pageReqVO);
        
        // 使用权限SQL进行查询
        return sdkOrderMapper.selectPageWithPermission(pageReqVO, permissionSql);
    }
    
    /**
     * 获取用户权限范围内的产品和区服条件（返回VO类型）
     */
    private List<SdkOrderCondition> getVoUserPermittedConditions(Long userId) {
        // 获取用户的权限范围

        List<LeadGroupOptVO> leadGroupOptVOS = sdkLeadGroupLinkMapper.selectListByOptUserId(userId);
        if (CollectionUtils.isEmpty(leadGroupOptVOS)) {
            return Collections.emptyList();
        }
        
        // 创建一个包含产品ID和区服名的条件列表，表示用户的权限范围
        return leadGroupOptVOS.stream()

            .map(link -> new  SdkOrderCondition(
                    link.getProductId(),
                    link.getServerName(),
                    link.getChannelCode()
                    ))
            .distinct()
            .collect(Collectors.toList());
    }
    
    /**
     * 创建空的分页结果
     */
    private PageResult<SdkOrderRespVo> createEmptyPageResult() {
        PageResult<SdkOrderRespVo> emptyResult = new PageResult<>();
        emptyResult.setList(new ArrayList<>());
        emptyResult.setTotal(0L);
        return emptyResult;
    }
    
    /**
     * 处理用户订单搜索条件
     */
    private PageResult<SdkOrderRespVo> processVoUserOrderSearchConditions(OrderPageReqVO pageReqVO, List<SdkOrderCondition> permittedConditions) {
        boolean hasUserProductId = pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty();
        boolean hasUserServerName = pageReqVO.getServerName() != null && !pageReqVO.getServerName().isEmpty();
        boolean hasUserChannelCode = pageReqVO.getChannelCode() != null && !pageReqVO.getChannelCode().isEmpty();
        
        // 如果用户没有指定产品ID、区服和渠道码，则直接使用权限范围内的条件查询
        if (!hasUserProductId && !hasUserServerName && !hasUserChannelCode) {
            return sdkOrderMapper.selectPageWithVoConditions(pageReqVO, permittedConditions);
        }
        
        // 复制一个新的 pageReqVO 对象，以便修改而不影响原始对象
        OrderPageReqVO filteredPageReqVO = BeanUtils.toBean(pageReqVO, OrderPageReqVO.class);
        
        // 筛选出符合条件的 SdkOrderCondition 列表
        List<SdkOrderCondition> filteredConditions = new ArrayList<>();
        
        // 如果用户指定了产品ID
        if (hasUserProductId) {
            List<Long> filteredProductIds = filterProductIds(pageReqVO.getProductId(), permittedConditions);
            if (filteredProductIds.isEmpty()) {
                return createEmptyPageResult();
            }
            filteredPageReqVO.setProductId(filteredProductIds);
            
            // 筛选出包含这些产品ID的条件
            for (SdkOrderCondition condition : permittedConditions) {
                if (filteredProductIds.contains(condition.getProductId())) {
                    // 如果用户指定了区服，则还需要匹配区服
                    if (hasUserServerName) {
                        if (pageReqVO.getServerName().equals(condition.getServerName())) {
                            // 如果用户指定了渠道码，则还需要匹配渠道码
                            if (hasUserChannelCode) {
                                if (pageReqVO.getChannelCode().contains(condition.getChannelCode())) {
                                    filteredConditions.add(condition);
                                }
                            } else {
                                filteredConditions.add(condition);
                            }
                        }
                    }
                    // 如果用户没有指定区服，但指定了渠道码
                    else if (hasUserChannelCode) {
                        if (pageReqVO.getChannelCode().contains(condition.getChannelCode())) {
                            filteredConditions.add(condition);
                        }
                    }
                    // 如果用户只指定了产品ID
                    else {
                        filteredConditions.add(condition);
                    }
                }
            }
        }
        // 如果用户没有指定产品ID，但指定了区服
        else if (hasUserServerName) {
            String userServerName = pageReqVO.getServerName();
            if (!hasServerNamePermission(permittedConditions, userServerName)) {
                return createEmptyPageResult();
            }
            
            // 筛选出匹配这个区服的条件
            for (SdkOrderCondition condition : permittedConditions) {
                if (userServerName.equals(condition.getServerName())) {
                    // 如果用户指定了渠道码，则还需要匹配渠道码
                    if (hasUserChannelCode) {
                        if (pageReqVO.getChannelCode().contains(condition.getChannelCode())) {
                            filteredConditions.add(condition);
                        }
                    } else {
                        filteredConditions.add(condition);
                    }
                }
            }
            
            // 获取所有匹配的产品ID
            List<Long> permittedProductIds = filteredConditions.stream()
                    .map(SdkOrderCondition::getProductId)
                    .distinct()
                    .collect(Collectors.toList());
            
            if (!permittedProductIds.isEmpty()) {
                filteredPageReqVO.setProductId(permittedProductIds);
            }
        }
        // 如果用户只指定了渠道码
        else if (hasUserChannelCode) {
            List<String> filteredChannelCodes = filterChannelCodes(pageReqVO.getChannelCode(), permittedConditions);
            if (filteredChannelCodes.isEmpty()) {
                return createEmptyPageResult();
            }
            filteredPageReqVO.setChannelCode(filteredChannelCodes);
            
            // 筛选出匹配这些渠道码的条件
            for (SdkOrderCondition condition : permittedConditions) {
                if (condition.getChannelCode() != null && filteredChannelCodes.contains(condition.getChannelCode())) {
                    filteredConditions.add(condition);
                }
            }
            
            // 获取所有匹配的产品ID
            List<Long> permittedProductIds = filteredConditions.stream()
                    .map(SdkOrderCondition::getProductId)
                    .distinct()
                    .collect(Collectors.toList());
            
            if (!permittedProductIds.isEmpty()) {
                filteredPageReqVO.setProductId(permittedProductIds);
            }
        }
        
        // 如果没有筛选出任何条件，则使用原始的权限条件
        if (filteredConditions.isEmpty()) {
            filteredConditions = permittedConditions;
        }
        
        // 使用 selectPageWithVoConditions 方法进行查询，保持SQL查询的一致性
        return sdkOrderMapper.selectPageWithVoConditions(filteredPageReqVO, filteredConditions);
    }

    @Override
    public SdkOrderTotalAmountRespVo getOrderPageTotalAmount(OrderPageReqVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String roleId = SecurityFrameworkUtils.getLoginUserRoleId();
        if (roleId == null) {
            throw exception(USER_ROLE_NOT_EXISTS);
        }

        // 使用新的权限系统生成权限SQL
        String permissionSql = dataPermissionService.generatePermissionSql(userId, "t",pageReqVO);
        
        // 使用权限SQL进行查询
        return sdkOrderMapper.selectPageTotalAmountWithPermission(pageReqVO, permissionSql);
    }
    
    /**
     * 创建空的总金额结果
     */
    private SdkOrderTotalAmountRespVo createEmptyTotalAmountResult() {
        SdkOrderTotalAmountRespVo emptyResult = new SdkOrderTotalAmountRespVo();
        emptyResult.setTotalAmount(0F);
        return emptyResult;
    }
    
    /**
     * 处理用户总金额搜索条件
     */
    private SdkOrderTotalAmountRespVo processVoUserTotalAmountSearchConditions(OrderPageReqVO pageReqVO, List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition> permittedConditions) {
        boolean hasUserProductId = pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty();
        boolean hasUserServerName = pageReqVO.getServerName() != null && !pageReqVO.getServerName().isBlank();
        boolean hasUserChannelCode = pageReqVO.getChannelCode() != null && !pageReqVO.getChannelCode().isEmpty();
        
        // 如果用户没有指定产品ID、区服和渠道码，则直接使用权限范围内的条件查询
        if (!hasUserProductId && !hasUserServerName && !hasUserChannelCode) {
            return sdkOrderMapper.selectPageTotalAmountWithVoConditions(pageReqVO, permittedConditions);
        }
        
        // 复制一个新的 pageReqVO 对象，以便修改而不影响原始对象
        OrderPageReqVO filteredPageReqVO = BeanUtils.toBean(pageReqVO, OrderPageReqVO.class);
        
        // 筛选出符合条件的 SdkOrderCondition 列表
        List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition> filteredConditions = new ArrayList<>();
        
        // 如果用户指定了产品ID
        if (hasUserProductId) {
            List<Long> filteredProductIds = filterProductIds(pageReqVO.getProductId(), permittedConditions);
            if (filteredProductIds.isEmpty()) {
                return createEmptyTotalAmountResult();
            }
            filteredPageReqVO.setProductId(filteredProductIds);
            
            // 筛选出包含这些产品ID的条件
            for (com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition condition : permittedConditions) {
                if (filteredProductIds.contains(condition.getProductId())) {
                    // 如果用户指定了区服，则还需要匹配区服
                    if (hasUserServerName) {
                        if (pageReqVO.getServerName().equals(condition.getServerName())) {
                            // 如果用户指定了渠道码，则还需要匹配渠道码
                            if (hasUserChannelCode) {
                                if (pageReqVO.getChannelCode().contains(condition.getChannelCode())) {
                                    filteredConditions.add(condition);
                                }
                            } else {
                                filteredConditions.add(condition);
                            }
                        }
                    }
                    // 如果用户没有指定区服，但指定了渠道码
                    else if (hasUserChannelCode) {
                        if (pageReqVO.getChannelCode().contains(condition.getChannelCode())) {
                            filteredConditions.add(condition);
                        }
                    }
                    // 如果用户只指定了产品ID
                    else {
                        filteredConditions.add(condition);
                    }
                }
            }
        }
        // 如果用户没有指定产品ID，但指定了区服
        else if (hasUserServerName) {
            String userServerName = pageReqVO.getServerName();
            if (!hasServerNamePermission(permittedConditions, userServerName)) {
                return createEmptyTotalAmountResult();
            }
            
            // 筛选出匹配这个区服的条件
            for (com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition condition : permittedConditions) {
                if (userServerName.equals(condition.getServerName())) {
                    // 如果用户指定了渠道码，则还需要匹配渠道码
                    if (hasUserChannelCode) {
                        if (pageReqVO.getChannelCode().contains(condition.getChannelCode())) {
                            filteredConditions.add(condition);
                        }
                    } else {
                        filteredConditions.add(condition);
                    }
                }
            }
            
            // 获取所有匹配的产品ID
            List<Long> permittedProductIds = filteredConditions.stream()
                    .map(com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition::getProductId)
                    .distinct()
                    .collect(Collectors.toList());
            
            if (!permittedProductIds.isEmpty()) {
                filteredPageReqVO.setProductId(permittedProductIds);
            }
        }
        // 如果用户只指定了渠道码
        else if (hasUserChannelCode) {
            List<String> filteredChannelCodes = filterChannelCodes(pageReqVO.getChannelCode(), permittedConditions);
            if (filteredChannelCodes.isEmpty()) {
                return createEmptyTotalAmountResult();
            }
            filteredPageReqVO.setChannelCode(filteredChannelCodes);
            
            // 筛选出匹配这些渠道码的条件
            for (com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition condition : permittedConditions) {
                if (condition.getChannelCode() != null && filteredChannelCodes.contains(condition.getChannelCode())) {
                    filteredConditions.add(condition);
                }
            }
            
            // 获取所有匹配的产品ID
            List<Long> permittedProductIds = filteredConditions.stream()
                    .map(com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition::getProductId)
                    .distinct()
                    .collect(Collectors.toList());
            
            if (!permittedProductIds.isEmpty()) {
                filteredPageReqVO.setProductId(permittedProductIds);
            }
        }
        
        // 如果没有筛选出任何条件，则使用原始的权限条件
        if (filteredConditions.isEmpty()) {
            filteredConditions = permittedConditions;
        }
        
        // 使用 selectPageTotalAmountWithVoConditions 方法进行查询，保持SQL查询的一致性
        return sdkOrderMapper.selectPageTotalAmountWithVoConditions(filteredPageReqVO, filteredConditions);
    }

    @Override
    public SdkOrderSummaryRespVO getOrderPaySummary(SdkOrderSummaryReqVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String roleId = SecurityFrameworkUtils.getLoginUserRoleId();
        if (roleId == null) {
            throw exception(USER_ROLE_NOT_EXISTS);
        }

        // 构建权限SQL条件
        String permissionSql = buildPermissionSql("t", pageReqVO.getUserIds());

        // 调用各个Mapper方法，传入权限SQL
        SdkOrderSummaryRespVO orderPaySummaryByDay = sdkOrderMapper.getOrderPaySummaryByDay(pageReqVO, permissionSql);
        SdkOrderSummaryRespVO orderPaySummaryByWeek = sdkOrderMapper.getOrderPaySummaryByWeek(pageReqVO, permissionSql);
        SdkOrderSummaryRespVO orderUserPaySummaryByDay = sdkOrderMapper.getOrderUserPaySummaryByDayWithPermission(pageReqVO, permissionSql);
        SdkOrderSummaryRespVO orderUserPaySummaryByWeek = sdkOrderMapper.getOrderUserPaySummaryByWeekWithPermission(pageReqVO, permissionSql);

        // 设置月度交易金额，传入权限SQL
        orderPaySummaryByDay.setMonthlyPrice(sdkOrderMapper.getMonthlyTradeAmountWithPermission(pageReqVO, permissionSql));
        orderPaySummaryByDay.setMonthlyPay(sdkOrderMapper.getMonthlyPayWithPermission(pageReqVO, permissionSql));
        orderPaySummaryByDay.setMonthlyPayUser(sdkOrderMapper.getMonthlyPayUsersWithPermission(pageReqVO, permissionSql));

        // 获取用户注册数据，传入权限SQL
        SdkUserSummaryReqVO userSummaryReqVO = BeanUtils.toBean(pageReqVO, SdkUserSummaryReqVO.class);
        String buildPermissionUserSql = buildPermissionUserSql("t", pageReqVO.getUserIds());
        SdkUserSummaryRespVO userRegistrationSummaryByDay = sdkUserMapper.getUserRegistrationSummaryByDay(pageReqVO, buildPermissionUserSql);
        SdkUserSummaryRespVO userRegistrationSummaryByWeek = sdkUserMapper.getUserRegistrationSummaryByWeek(pageReqVO, buildPermissionUserSql);
        orderPaySummaryByDay.setTodayRegistration(userRegistrationSummaryByDay.getTodayRegistration());
        orderPaySummaryByDay.setWeekRegistration(userRegistrationSummaryByWeek.getWeekRegistration());

        // 获取月度注册用户数据，传入权限SQL
        List<SdkUserSummaryMonthRespVO> userMonthlyData = sdkUserMapper.getMonthlyRegisterUsersWithPermission(userSummaryReqVO, buildPermissionUserSql);
        List<SdkOrderSummaryMonthRespVO> convertedData = BeanUtils.toBean(userMonthlyData, SdkOrderSummaryMonthRespVO.class);
        orderPaySummaryByDay.setMonthlyRegistration(convertedData);

        // 获取活跃用户数据
        SdkUserSummaryReqVO activeUserReqVO = new SdkUserSummaryReqVO();
        activeUserReqVO.setPayTime(pageReqVO.getPayTime());
        activeUserReqVO.setProductId(pageReqVO.getProductId());
        
        // 获取日活跃和周活跃用户数据，传入权限SQL
        SdkUserSummaryRespVO dailyActiveUsersSummary = sdkUserMapper.getDailyActiveUsersSummaryWithPermission(activeUserReqVO, buildPermissionUserSql);
        if (dailyActiveUsersSummary != null) {
            orderPaySummaryByDay.setTodayActiveUsers(dailyActiveUsersSummary.getTodayActiveUsers());
        }
        
        SdkUserSummaryRespVO weeklyActiveUsersSummary = sdkUserMapper.getWeeklyActiveUsersSummaryWithPermission(activeUserReqVO, buildPermissionUserSql);
        if (weeklyActiveUsersSummary != null) {
            orderPaySummaryByDay.setWeekActiveUsers(weeklyActiveUsersSummary.getWeekActiveUsers());
        }
        
        // 获取月度活跃用户趋势，传入权限SQL
        List<SdkUserSummaryMonthRespVO> monthlyActiveUsersTrend = sdkUserMapper.getMonthlyActiveUsersTrendWithPermission(activeUserReqVO, buildPermissionUserSql);
        if (monthlyActiveUsersTrend != null && !monthlyActiveUsersTrend.isEmpty()) {
            List<SdkOrderSummaryMonthRespVO> convertedActiveUserData = BeanUtils.toBean(monthlyActiveUsersTrend, SdkOrderSummaryMonthRespVO.class);
            orderPaySummaryByDay.setMonthlyActiveUsers(convertedActiveUserData);
        }

        // 整合所有数据
        orderPaySummaryByDay.setWeekPay(orderPaySummaryByWeek.getWeekPay());
        orderPaySummaryByDay.setWeekPrice(orderPaySummaryByWeek.getWeekPrice());
        orderPaySummaryByDay.setTodayPayUser(orderUserPaySummaryByDay.getTodayPayUser());
        orderPaySummaryByDay.setWeekPayUser(orderUserPaySummaryByWeek.getWeekPayUser());
        
        return orderPaySummaryByDay;
    }

    /**
     * 构建权限SQL条件
     * @param tableAlias 表别名
     * @param userIds 用户ID列表，用于多用户查询场景
     * @return 权限SQL条件
     */
    private String buildPermissionSql(String tableAlias, List<Long> userIds) {
        String userIdsCondition = "";
        if (!CollectionUtils.isEmpty(userIds)) {
            userIdsCondition = "AND urb.user_id IN (" + CollUtil.join(userIds, ",") + ") ";
        } else {
            // 如果没有指定用户ID，则使用当前登录用户的ID
            userIdsCondition = "AND urb.user_id = " + SecurityFrameworkUtils.getLoginUserId() + " ";
        }

        return String.format(
            "EXISTS (" +
                "SELECT 1 FROM qsdk_user_resource_binding urb " +
                "WHERE 1=1 " +
                "AND urb.deleted = 0 AND urb.status = 1 " +
                "%s" + // 用户ID条件
                "AND (" +
                    // 运营人员权限：产品+渠道匹配
                    "(urb.resource_type = 'PRODUCT_CHANNEL' " +
                    "AND %s.productId = urb.product_id " +
                    "AND %s.channelCode = urb.channel_code) " +
                    
                    // 运维人员权限：产品+渠道+区服匹配  
                    "OR (urb.resource_type = 'SERVER' " +
                    "AND %s.productId = urb.product_id " +
                    "AND %s.channelCode = urb.channel_code " +
                    "AND %s.serverName = urb.server_name) " +
                ")" +
            ")",
            userIdsCondition,
            tableAlias, tableAlias,  // PRODUCT_CHANNEL 条件
            tableAlias, tableAlias, tableAlias  // SERVER 条件
        );
    }

    private String buildPermissionUserSql(String tableAlias, List<Long> userIds) {
        String userIdsCondition = "";
        if (!CollectionUtils.isEmpty(userIds)) {
            userIdsCondition = "AND urb.user_id IN (" + CollUtil.join(userIds, ",") + ") ";
        } else {
            // 如果没有指定用户ID，则使用当前登录用户的ID
            userIdsCondition = "AND urb.user_id = " + SecurityFrameworkUtils.getLoginUserId() + " ";
        }

        return String.format(
                "EXISTS (" +
                        "SELECT 1 FROM qsdk_user_resource_binding urb " +
                        "WHERE 1=1 " +
                        "AND urb.deleted = 0 AND urb.status = 1 " +
                        "%s" + // 用户ID条件
                        "AND (" +
                        // 运营人员权限：产品+渠道匹配
                        "(urb.resource_type = 'PRODUCT_CHANNEL' " +
                        "AND %s.productId = urb.product_id " +
                        "AND %s.channelCode = urb.channel_code) " +

                        // 运维人员权限：产品+渠道+区服匹配
                        "OR (urb.resource_type = 'SERVER' " +
                        "AND %s.productId = urb.product_id " +
                        "AND %s.channelCode = urb.channel_code " +
                        "  ) " +
                        ")" +
                        ")",
                userIdsCondition,
                tableAlias, tableAlias,  // PRODUCT_CHANNEL 条件
                tableAlias, tableAlias, tableAlias  // SERVER 条件
        );
    }

    /**
     * 创建空的订单支付汇总结果，用于异常情况
     */
    private SdkOrderSummaryRespVO createEmptyOrderPaySummary() {
        SdkOrderSummaryRespVO emptyResult = new SdkOrderSummaryRespVO();
        emptyResult.setTodayPrice(0F);
        emptyResult.setTodayPay(0L);
        emptyResult.setWeekPrice(0F);
        emptyResult.setWeekPay(0L);
        emptyResult.setTodayPayUser(0L);
        emptyResult.setWeekPayUser(0L);
        emptyResult.setTodayRegistration(0L);
        emptyResult.setWeekRegistration(0L);
        emptyResult.setMonthlyPrice(new ArrayList<>());
        emptyResult.setMonthlyPay(new ArrayList<>());
        emptyResult.setMonthlyPayUser(new ArrayList<>());
        emptyResult.setMonthlyRegistration(new ArrayList<>());
        return emptyResult;
    }
    
    /**
     * 处理用户订单支付汇总条件
     */
    private SdkOrderSummaryRespVO processVoUserOrderPaySummary(SdkOrderSummaryReqVO pageReqVO, List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition> permittedConditions) {
        // 复制一个新的 pageReqVO 对象，以便修改而不影响原始对象
        SdkOrderSummaryReqVO filteredReqVO = BeanUtils.toBean(pageReqVO, SdkOrderSummaryReqVO.class);
        
        // 筛选出符合条件的条件对象
        List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition> filteredConditions = new ArrayList<>();
        
        if (filteredReqVO.getProductId() != null && !filteredReqVO.getProductId().isEmpty()) {
            // 如果用户指定了产品ID，需要找出这些产品ID中有权限的部分
            List<Long> permittedProductIds = getPermittedProductIds(filteredReqVO.getProductId(), permittedConditions);
            if (permittedProductIds.isEmpty()) {
                return createEmptyOrderPaySummary();
            }
            
            // 如果还指定了区服名
            if (filteredReqVO.getServerName() != null && !filteredReqVO.getServerName().isEmpty()) {
                String userServerName = filteredReqVO.getServerName();
                
                // 筛选出有权限的产品ID和区服的组合
                for (com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition condition : permittedConditions) {
                    if (permittedProductIds.contains(condition.getProductId()) && userServerName.equals(condition.getServerName())) {
                        filteredConditions.add(condition);
                    }
                }
                
                if (filteredConditions.isEmpty()) {
                    return createEmptyOrderPaySummary();
                }
            } 
            // 如果指定了区服列表
            else if (filteredReqVO.getServers() != null && !filteredReqVO.getServers().isEmpty()) {
                List<String> userServers = filteredReqVO.getServers();
                
                // 筛选出有权限的产品ID和区服的组合
                for (com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition condition : permittedConditions) {
                    if (permittedProductIds.contains(condition.getProductId()) && userServers.contains(condition.getServerName())) {
                        filteredConditions.add(condition);
                    }
                }
                
                if (filteredConditions.isEmpty()) {
                    return createEmptyOrderPaySummary();
                }
            }
            // 如果只指定了产品ID，找出这些产品ID对应的所有有权限的区服
            else {
                for (com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition condition : permittedConditions) {
                    if (permittedProductIds.contains(condition.getProductId())) {
                        filteredConditions.add(condition);
                    }
                }
            }
            
            // 更新过滤后的条件到请求对象中
            filteredReqVO.setProductId(filteredConditions.stream()
                    .map(com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition::getProductId)
                    .distinct()
                    .collect(Collectors.toList()));
        } 
        // 如果用户只指定了区服
        else if ((filteredReqVO.getServerName() != null && !filteredReqVO.getServerName().isEmpty()) || 
                 (filteredReqVO.getServers() != null && !filteredReqVO.getServers().isEmpty())) {
            
            List<String> userServers = new ArrayList<>();
            if (filteredReqVO.getServerName() != null && !filteredReqVO.getServerName().isEmpty()) {
                userServers.add(filteredReqVO.getServerName());
            } else if (filteredReqVO.getServers() != null && !filteredReqVO.getServers().isEmpty()) {
                userServers.addAll(filteredReqVO.getServers());
            }
            
            // 筛选出有权限的区服对应的条件
            for (com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition condition : permittedConditions) {
                if (userServers.contains(condition.getServerName())) {
                    filteredConditions.add(condition);
                }
            }
            
            if (filteredConditions.isEmpty()) {
                return createEmptyOrderPaySummary();
            }
            
            // 更新过滤后的条件到请求对象中
            filteredReqVO.setProductId(filteredConditions.stream()
                    .map(com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition::getProductId)
                    .distinct()
                    .collect(Collectors.toList()));
            
            // 如果原始请求中有单个区服名，保留它；否则，使用所有筛选出的区服
            if (filteredReqVO.getServerName() == null || filteredReqVO.getServerName().isEmpty()) {
                filteredReqVO.setServers(filteredConditions.stream()
                        .map(com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition::getServerName)
                        .distinct()
                        .collect(Collectors.toList()));
            }
        } 
        // 如果用户既没有指定产品ID也没有指定区服，使用所有有权限的条件
        else {
            filteredConditions.addAll(permittedConditions);
            
            // 更新所有有权限的产品ID到请求对象中
            filteredReqVO.setProductId(filteredConditions.stream()
                    .map(com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition::getProductId)
                    .distinct()
                    .collect(Collectors.toList()));
            
            // 使用所有有权限的区服
            filteredReqVO.setServers(filteredConditions.stream()
                    .map(com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition::getServerName)
                    .distinct()
                    .collect(Collectors.toList()));
        }
        
        // 使用筛选后的条件查询订单支付汇总
        return getOrderPaySummaryWithoutPermissionCheck(filteredReqVO, filteredConditions);
    }
    
    /**
     * 获取用户有权限的产品ID列表
     */
    private List<Long> getPermittedProductIds(List<Long> productIds, List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition> permittedConditions) {
        if (CollectionUtils.isEmpty(productIds) || CollectionUtils.isEmpty(permittedConditions)) {
            return Collections.emptyList();
        }
        
        // 获取权限范围内的产品ID
        List<Long> permittedProductIds = permittedConditions.stream()
                .map(SdkOrderCondition::getProductId)
                .collect(Collectors.toList());
        
        // 过滤出用户请求的产品ID中有权限的部分
        return productIds.stream()
                .filter(permittedProductIds::contains)
                .collect(Collectors.toList());
    }
    
    /**
     * 过滤用户请求的渠道码列表，只保留有权限的部分
     */
    private List<String> filterChannelCodes(List<String> channelCodes, List<SdkOrderCondition> permittedConditions) {
        if (CollectionUtils.isEmpty(channelCodes) || CollectionUtils.isEmpty(permittedConditions)) {
            return Collections.emptyList();
        }
        
        // 获取权限范围内的渠道码
        List<String> permittedChannelCodes = permittedConditions.stream()
                .map(SdkOrderCondition::getChannelCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        // 过滤出用户请求的渠道码中有权限的部分
        return channelCodes.stream()
                .filter(permittedChannelCodes::contains)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据渠道码列表获取有权限的产品ID列表
     */
    private List<Long> getPermittedProductIdsByChannelCodes(List<SdkOrderCondition> permittedConditions, List<String> channelCodes) {
        if (CollectionUtils.isEmpty(permittedConditions) || CollectionUtils.isEmpty(channelCodes)) {
            return Collections.emptyList();
        }
        
        // 获取指定渠道码列表对应的产品ID列表
        return permittedConditions.stream()
                .filter(condition -> condition.getChannelCode() != null && channelCodes.contains(condition.getChannelCode()))
                .map(SdkOrderCondition::getProductId)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取用户有权限的所有产品ID
     */
    private List<Long> getAllPermittedProductIds(List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition> permittedConditions) {
        // 从permittedConditions中提取所有产品ID
        return permittedConditions.stream()
            .map(com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition::getProductId)
            .distinct()
            .collect(Collectors.toList());
    }

    /**
     * 不进行权限检查的订单支付汇总查询
     */
    private SdkOrderSummaryRespVO getOrderPaySummaryWithoutPermissionCheck(SdkOrderSummaryReqVO pageReqVO) {
        return getOrderPaySummaryWithoutPermissionCheck(pageReqVO, null);
    }

    /**
     * 不进行权限检查的订单支付汇总查询，但提供产品ID和区服的关联条件
     *
     * @param pageReqVO 查询条件
     * @param conditions 产品ID和区服的关联条件，为null时不使用关联条件
     * @return 订单支付汇总信息
     */
    private SdkOrderSummaryRespVO getOrderPaySummaryWithoutPermissionCheck(SdkOrderSummaryReqVO pageReqVO, List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition> conditions) {
        // 将VO类型的条件转换为DO类型的条件
        List<SdkOrderCondition> sdkConditions = null;
        if (conditions != null) {
            sdkConditions = conditions.stream()
                .map(c -> new SdkOrderCondition(c.getProductId(), c.getServerName(),c.getChannelCode()))
                .collect(Collectors.toList());
        }

        // 使用新增的方法来传递产品ID和区服的关联条件
        SdkOrderSummaryRespVO orderPaySummaryByDay = sdkOrderMapper.getOrderPaySummaryByDay(pageReqVO, sdkConditions);
        SdkOrderSummaryRespVO orderPaySummaryByWeek = sdkOrderMapper.getOrderPaySummaryByWeek(pageReqVO, sdkConditions);
        SdkOrderSummaryRespVO orderUserPaySummaryByDay = sdkOrderMapper.getOrderUserPaySummaryByDay(pageReqVO, sdkConditions);
        SdkOrderSummaryRespVO orderUserPaySummaryByWeek = sdkOrderMapper.getOrderUserPaySummaryByWeek(pageReqVO, sdkConditions);

        orderPaySummaryByDay.setMonthlyPrice(sdkOrderMapper.getMonthlyTradeAmount(pageReqVO));
        orderPaySummaryByDay.setMonthlyPay(sdkOrderMapper.getMonthlyPay(pageReqVO));
        orderPaySummaryByDay.setMonthlyPayUser(sdkOrderMapper.getMonthlyPayUsers(pageReqVO));

        // 获取用户注册数据
        SdkUserSummaryReqVO userSummaryReqVO = BeanUtils.toBean(pageReqVO, SdkUserSummaryReqVO.class);
        SdkUserSummaryRespVO userRegistrationSummaryByDay = sdkUserMapper.getUserRegistrationSummaryByDay(pageReqVO);
        SdkUserSummaryRespVO userRegistrationSummaryByWeek = sdkUserMapper.getUserRegistrationSummaryByWeek(pageReqVO);
        orderPaySummaryByDay.setTodayRegistration(userRegistrationSummaryByDay.getTodayRegistration());
        orderPaySummaryByDay.setWeekRegistration(userRegistrationSummaryByWeek.getWeekRegistration());

        List<SdkUserSummaryMonthRespVO> userMonthlyData = sdkUserMapper.getMonthlyRegisterUsersWithConditions(userSummaryReqVO, sdkConditions);
        List<SdkOrderSummaryMonthRespVO> convertedData = BeanUtils.toBean(userMonthlyData, SdkOrderSummaryMonthRespVO.class);

        orderPaySummaryByDay.setMonthlyRegistration(convertedData);


        // 获取日活跃用户数据
        SdkUserSummaryReqVO activeUserReqVO = new SdkUserSummaryReqVO();
        activeUserReqVO.setPayTime(pageReqVO.getPayTime());
        activeUserReqVO.setProductId(pageReqVO.getProductId());

        // 获取日活跃用户数据
        SdkUserSummaryRespVO dailyActiveUsersSummary = sdkUserMapper.getDailyActiveUsersSummary(activeUserReqVO, sdkConditions);
        if (dailyActiveUsersSummary != null) {
            orderPaySummaryByDay.setTodayActiveUsers(dailyActiveUsersSummary.getTodayActiveUsers());
        }
        
        // 获取周活跃用户数据
        SdkUserSummaryRespVO weeklyActiveUsersSummary = sdkUserMapper.getWeeklyActiveUsersSummary(activeUserReqVO, sdkConditions);
        if (weeklyActiveUsersSummary != null) {
            orderPaySummaryByDay.setWeekActiveUsers(weeklyActiveUsersSummary.getWeekActiveUsers());
        }
        
        // 获取月度活跃用户趋势
        List<SdkUserSummaryMonthRespVO> monthlyActiveUsersTrend = sdkUserMapper.getMonthlyActiveUsersTrend(activeUserReqVO, sdkConditions);
        if (monthlyActiveUsersTrend != null && !monthlyActiveUsersTrend.isEmpty()) {
            // 将月度活跃用户趋势数据转换为SdkOrderSummaryMonthRespVO类型
            List<SdkOrderSummaryMonthRespVO> convertedActiveUserData = BeanUtils.toBean(monthlyActiveUsersTrend, SdkOrderSummaryMonthRespVO.class);
            orderPaySummaryByDay.setMonthlyActiveUsers(convertedActiveUserData);
        }

        // 整合所有数据
        orderPaySummaryByDay.setWeekPay(orderPaySummaryByWeek.getWeekPay());
        orderPaySummaryByDay.setWeekPrice(orderPaySummaryByWeek.getWeekPrice());
        orderPaySummaryByDay.setTodayPayUser(orderUserPaySummaryByDay.getTodayPayUser());
        orderPaySummaryByDay.setWeekPayUser(orderUserPaySummaryByWeek.getWeekPayUser());
        return orderPaySummaryByDay;
    }

    @Override
    public List<String> getServerByProductId(List<Long> productId) {

        return sdkOrderMapper.selectSeverByProductId(productId).stream().filter(x->x != null ).collect(Collectors.toList());
    }

    /**
     * 检查产品ID和区服名是否在权限范围内
     */
    private boolean isInPermittedConditions(List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition> permittedConditions, String productId, String serverName) {
        return permittedConditions.stream()
            .anyMatch(condition -> 
                condition.getProductId().equals(productId) && 
                condition.getServerName().equals(serverName));
    }
    
    /**
     * 检查是否有特定产品ID的任何区服权限
     */
    private boolean hasProductIdPermission(List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition> permittedConditions, Long productId) {
        return permittedConditions.stream()
            .anyMatch(condition -> condition.getProductId().equals(productId));
    }
    
    /**
     * 检查是否有特定区服的权限
     */
    private boolean hasServerNamePermission(List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition> permittedConditions, String serverName) {
        return permittedConditions.stream()
            .anyMatch(condition -> condition.getServerName().equals(serverName));
    }

    /**
     * 根据产品ID获取有权限的区服名
     */
    private List<String> getPermittedServerNamesByProductIds(List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition> permittedConditions, List<String> productIds) {
        return permittedConditions.stream()
            .filter(condition -> productIds.contains(condition.getProductId()))
            .map(com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition::getServerName)
            .distinct()
            .collect(Collectors.toList());
    }
    
    /**
     * 根据区服名获取有权限的产品ID
     */
    private List<Long> getPermittedProductIdsByServerName(List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition> permittedConditions, String serverName) {
        return permittedConditions.stream()
            .filter(condition -> condition.getServerName().equals(serverName))
            .map(com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition::getProductId)
            .distinct()
            .collect(Collectors.toList());
    }

    /**
     * 过滤有权限的产品ID列表
     */
    private List<Long> filterProductIds(List<Long> productIds, List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition> permittedConditions) {
        return productIds.stream()
            .filter(productId -> hasProductIdPermission(permittedConditions, productId))
            .collect(Collectors.toList());
    }
    
    /**
     * 根据区服名过滤有权限的产品ID列表
     */
    private List<String> filterProductsWithServerName(List<String> productIds, String serverName, List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderCondition> permittedConditions) {
        return productIds.stream()
            .filter(productId -> isInPermittedConditions(permittedConditions, productId, serverName))
            .collect(Collectors.toList());
    }

    /**
     * 获取运营用户权限范围内的产品和渠道条件
     */
    public List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> getOperationUserPermittedConditions(Long userId) {
        // 获取用户的运营权限范围，使用sdkOptMapper
        List<SdkOperationPermissionVO> operationPermissions = sdkOptMapper.selectOperationPermission(userId);
        if (CollectionUtils.isEmpty(operationPermissions)) {
            return Collections.emptyList();
        }
        
        // 创建一个包含产品ID和渠道代码的条件列表，表示用户的权限范围
        return operationPermissions.stream()
            .filter(permission -> permission != null)
            .filter(permission -> permission.getProductId() != null && permission.getChannelCode() != null && !permission.getChannelCode().isEmpty())
            .map(permission -> new com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition(
                    permission.getProductId(),
                    permission.getChannelCode()))
            .distinct()
            .collect(Collectors.toList());
    }
    
    /**
     * 处理运营用户订单搜索条件
     */
    private PageResult<SdkOrderRespVo> processOperationUserOrderSearchConditions(OrderPageReqVO pageReqVO, List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> permittedConditions) {
        boolean hasUserProductId = pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty();
        boolean hasUserChannelCode = pageReqVO.getChannelCode() != null && !pageReqVO.getChannelCode().isEmpty();
        
        // 如果用户没有指定产品ID和渠道代码，则使用权限范围内的条件查询
        if (!hasUserProductId && !hasUserChannelCode) {
            // 使用新的查询方法
            return sdkOrderMapper.selectPageWithOperationConditions(pageReqVO, permittedConditions);
        }
        
        // 复制一个新的 pageReqVO 对象，以便修改而不影响原始对象
        OrderPageReqVO filteredPageReqVO = BeanUtils.toBean(pageReqVO, OrderPageReqVO.class);
        
        // 提取所有有权限的产品ID和渠道代码
        List<Long> permittedProductIds = permittedConditions.stream()
            .map(com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition::getProductId)
            .distinct()
            .collect(Collectors.toList());
            
        List<String> permittedChannelCodes = permittedConditions.stream()
            .map(com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition::getChannelCode)
            .distinct()
            .collect(Collectors.toList());
        
        // 使用带精确匹配权限控制的查询方法，传递完整的权限组合
        return sdkOrderMapper.selectPageWithProductAndChannels(
            filteredPageReqVO, 
            permittedProductIds, 
            permittedChannelCodes, 
            permittedConditions
        );
    }

    /**
     * 检查是否有特定产品ID和渠道代码的权限
     */
    private boolean hasOperationPermission(List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> permittedConditions, 
                                        String productId, String channelCode) {
        return permittedConditions.stream()
            .anyMatch(condition -> 
                condition.getProductId().equals(productId) && 
                condition.getChannelCode().equals(channelCode));
    }

    /**
     * 处理运营人员订单支付汇总条件
     */
    private SdkOrderSummaryRespVO processOperationUserOrderPaySummary(SdkOrderSummaryReqVO pageReqVO, 
                               List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> permittedConditions) {
        // 复制一个新的 pageReqVO 对象，以便修改而不影响原始对象
        SdkOrderSummaryReqVO filteredReqVO = BeanUtils.toBean(pageReqVO, SdkOrderSummaryReqVO.class);
        
        // 提取所有有权限的产品ID
        List<Long> permittedProductIds = permittedConditions.stream()
            .map(com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition::getProductId)
            .distinct()
            .collect(Collectors.toList());
            
        // 提取所有有权限的渠道代码
        List<String> permittedChannelCodes = permittedConditions.stream()
            .map(com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition::getChannelCode)
            .distinct()
            .collect(Collectors.toList());
            
        // 检查用户是否指定了产品ID和渠道代码
        boolean hasUserProductId = pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty();
        boolean hasUserChannelCode = pageReqVO.getChannelCode() != null && !pageReqVO.getChannelCode().isEmpty();
        
        // 处理产品ID筛选
        if (hasUserProductId) {
            List<Long> filteredProductIds = pageReqVO.getProductId().stream()
                .filter(permittedProductIds::contains)
                .collect(Collectors.toList());
                
            if (filteredProductIds.isEmpty()) {
                return createEmptyOrderPaySummary();
            }
            
            filteredReqVO.setProductId(filteredProductIds);
        } else {
            // 如果用户没有指定产品ID，则使用所有有权限的产品ID
            filteredReqVO.setProductId(permittedProductIds);
        }
        
        // 处理渠道代码筛选
        if (hasUserChannelCode) {
            List<String> filteredChannelCodes = pageReqVO.getChannelCode().stream()
                .filter(permittedChannelCodes::contains)
                .collect(Collectors.toList());
                
            if (filteredChannelCodes.isEmpty()) {
                return createEmptyOrderPaySummary();
            }
            
            filteredReqVO.setChannelCode(filteredChannelCodes);
        } else {
            // 如果用户没有指定渠道代码，则使用所有有权限的渠道代码
            filteredReqVO.setChannelCode(permittedChannelCodes);
        }
        
        // 使用条件进行查询
        // 由于底层接口操作的是产品和服务器而不是产品和渠道，所以需要转换
        return getOperationOrderPaySummary(filteredReqVO, permittedConditions);
    }
    
    /**
     * 获取运营用户订单支付汇总，使用产品ID和渠道代码
     */
    private SdkOrderSummaryRespVO getOperationOrderPaySummary(SdkOrderSummaryReqVO pageReqVO, 
                                  List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> conditions) {
        // 初始化一个结果对象
        SdkOrderSummaryRespVO result = createEmptyOrderPaySummary();
        
        if (CollectionUtils.isEmpty(conditions)) {
            return result;
        }
        
        // 直接使用调用底层查询方法的方式，不转换为SdkOrderCondition
        SdkOrderSummaryRespVO dailySummary = sdkOrderMapper.getOperationOrderPaySummaryByDay(pageReqVO, conditions);
        SdkOrderSummaryRespVO weeklySummary = sdkOrderMapper.getOperationOrderPaySummaryByWeek(pageReqVO, conditions);
        SdkOrderSummaryRespVO userPaySummaryByDay = sdkOrderMapper.getOperationUserPaySummaryByDay(pageReqVO, conditions);
        SdkOrderSummaryRespVO userPaySummaryByWeek = sdkOrderMapper.getOperationUserPaySummaryByWeek(pageReqVO, conditions);
        
        // 获取月度数据
        List<SdkOrderSummaryMonthRespVO> monthlyTradeAmount = sdkOrderMapper.getOperationMonthlyTradeAmount(pageReqVO, conditions);
        List<SdkOrderSummaryMonthRespVO> monthlyPay = sdkOrderMapper.getOperationMonthlyPay(pageReqVO, conditions);
        List<SdkOrderSummaryMonthRespVO> monthlyPayUsers = sdkOrderMapper.getOperationMonthlyPayUsers(pageReqVO, conditions);
        
        // 整合数据
        if (dailySummary != null) {
            result.setTodayPrice(dailySummary.getTodayPrice());
            result.setTodayPay(dailySummary.getTodayPay());
        }
        
        if (weeklySummary != null) {
            result.setWeekPrice(weeklySummary.getWeekPrice());
            result.setWeekPay(weeklySummary.getWeekPay());
        }
        
        if (userPaySummaryByDay != null) {
            result.setTodayPayUser(userPaySummaryByDay.getTodayPayUser());
        }
        
        if (userPaySummaryByWeek != null) {
            result.setWeekPayUser(userPaySummaryByWeek.getWeekPayUser());
        }
        
        // 设置月度数据
        if (monthlyTradeAmount != null) {
            result.setMonthlyPrice(monthlyTradeAmount);
        }
        
        if (monthlyPay != null) {
            result.setMonthlyPay(monthlyPay);
        }
        
        if (monthlyPayUsers != null) {
            result.setMonthlyPayUser(monthlyPayUsers);
        }
        
        // 获取用户注册和活跃数据
        SdkUserSummaryReqVO userSummaryReqVO = BeanUtils.toBean(pageReqVO, SdkUserSummaryReqVO.class);
        SdkUserSummaryRespVO userRegistrationSummaryByDay = sdkUserMapper.getUserRegistrationSummaryByDay(pageReqVO);
        SdkUserSummaryRespVO userRegistrationSummaryByWeek = sdkUserMapper.getUserRegistrationSummaryByWeek(pageReqVO);
        
        if (userRegistrationSummaryByDay != null) {
            result.setTodayRegistration(userRegistrationSummaryByDay.getTodayRegistration());
        }
        
        if (userRegistrationSummaryByWeek != null) {
            result.setWeekRegistration(userRegistrationSummaryByWeek.getWeekRegistration());
        }
        
        // 获取月度注册用户数据
        List<SdkUserSummaryMonthRespVO> userMonthlyData = sdkUserMapper.getMonthlyRegisterUsers(userSummaryReqVO);
        if (userMonthlyData != null) {
            List<SdkOrderSummaryMonthRespVO> convertedData = BeanUtils.toBean(userMonthlyData, SdkOrderSummaryMonthRespVO.class);
            result.setMonthlyRegistration(convertedData);
        }
        
        // 获取活跃用户数据
        SdkUserLiveSummaryRespVO activeUserSummary = sdkUserMapper.getOperationActiveUserSummary(pageReqVO, conditions);
        if (activeUserSummary != null) {
            result.setTodayActiveUsers(activeUserSummary.getTodayLive());
            result.setWeekActiveUsers(activeUserSummary.getWeekLive());
            result.setMonthlyActiveUsers(activeUserSummary.getMonthlyLive());
        }
        
        return result;
    }

    /**
     * 处理运营用户总金额搜索条件
     */
    private SdkOrderTotalAmountRespVo processOperationUserTotalAmountSearchConditions(OrderPageReqVO pageReqVO, List<com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition> permittedConditions) {
        boolean hasUserProductId = pageReqVO.getProductId() != null && !pageReqVO.getProductId().isEmpty();
        boolean hasUserChannelCode = pageReqVO.getChannelCode() != null && !pageReqVO.getChannelCode().isEmpty();
        
        // 如果用户没有指定产品ID和渠道代码，则使用权限范围内的条件查询
        if (!hasUserProductId && !hasUserChannelCode) {
            // 使用新的查询方法
            return sdkOrderMapper.selectPageTotalAmountWithOperationConditions(pageReqVO, permittedConditions);
        }
        
        // 复制一个新的 pageReqVO 对象，以便修改而不影响原始对象
        OrderPageReqVO filteredPageReqVO = BeanUtils.toBean(pageReqVO, OrderPageReqVO.class);
        
        // 提取所有有权限的产品ID和渠道代码
        List<Long> permittedProductIds = permittedConditions.stream()
            .map(com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition::getProductId)
            .distinct()
            .collect(Collectors.toList());
            
        List<String> permittedChannelCodes = permittedConditions.stream()
            .map(com.mandong.api.module.erp.controller.admin.order.vo.SdkOperationCondition::getChannelCode)
            .distinct()
            .collect(Collectors.toList());
        
        // 使用带精确匹配权限控制的查询方法，传递完整的权限组合
        return sdkOrderMapper.selectPageTotalAmountWithProductAndChannels(
            filteredPageReqVO, 
            permittedProductIds, 
            permittedChannelCodes, 
            permittedConditions
        );
    }
}