package com.mandong.api.module.erp.service.permission;

import com.mandong.api.module.erp.dal.dataobject.permission.CrossDeptPermissionDO;

import java.util.List;

/**
 * 跨部门权限服务
 */
public interface CrossDeptPermissionService {
    
    /**
     * 检查用户是否有管理指定部门的权限
     */
    boolean hasManagePermission(Long userId, Long targetDeptId, String permissionType);
    
    /**
     * 检查用户是否有带队管理权限
     */
    boolean hasLeadGroupManagePermission(Long userId, Long targetDeptId);
    
    /**
     * 检查用户是否有运维组管理权限
     */
    boolean hasOptGroupManagePermission(Long userId, Long targetDeptId);
    
    /**
     * 获取用户可管理的部门列表
     */
    List<Long> getManageableDeptIds(Long userId, String permissionType);
    
    /**
     * 获取用户的跨部门权限列表
     */
    List<CrossDeptPermissionDO> getUserCrossDeptPermissions(Long userId);
    
    /**
     * 设置用户的跨部门权限
     */
    void setCrossDeptPermissions(Long userId, List<CrossDeptPermissionDO> permissions);
    
    /**
     * 删除用户的跨部门权限
     */
    void deleteCrossDeptPermissions(Long userId, String permissionType);
} 