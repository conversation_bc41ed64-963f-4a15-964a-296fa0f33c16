package com.mandong.api.module.erp.service.permission;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mandong.api.module.erp.dal.dataobject.permission.CrossDeptPermissionDO;
import com.mandong.api.module.erp.dal.sdkMysql.permission.CrossDeptPermissionMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 跨部门权限服务实现类
 */
@Service
@Slf4j
@DS("sdkDB")
public class CrossDeptPermissionServiceImpl implements CrossDeptPermissionService {

    @Resource
    private CrossDeptPermissionMapper crossDeptPermissionMapper;

    // 权限类型常量
    private static final String LEAD_GROUP_MANAGE = "LEAD_GROUP_MANAGE";
    private static final String OPT_GROUP_MANAGE = "OPT_GROUP_MANAGE";

    @Override
    public boolean hasManagePermission(Long userId, Long targetDeptId, String permissionType) {
        List<CrossDeptPermissionDO> permissions = crossDeptPermissionMapper.selectList(
            new QueryWrapper<CrossDeptPermissionDO>()
                .eq("source_user_id", userId)
                .eq("target_dept_id", targetDeptId)
                .eq("permission_type", permissionType)
                .eq("status", 1)
                .eq("deleted", false)
        );
        
        return !CollectionUtils.isEmpty(permissions);
    }

    @Override
    public boolean hasLeadGroupManagePermission(Long userId, Long targetDeptId) {
        return hasManagePermission(userId, targetDeptId, LEAD_GROUP_MANAGE);
    }

    @Override
    public boolean hasOptGroupManagePermission(Long userId, Long targetDeptId) {
        return hasManagePermission(userId, targetDeptId, OPT_GROUP_MANAGE);
    }

    @Override
    public List<Long> getManageableDeptIds(Long userId, String permissionType) {
        List<CrossDeptPermissionDO> permissions = crossDeptPermissionMapper.selectList(
            new QueryWrapper<CrossDeptPermissionDO>()
                .eq("source_user_id", userId)
                .eq("permission_type", permissionType)
                .eq("status", 1)
                .eq("deleted", false)
        );
        
        return permissions.stream()
                .map(CrossDeptPermissionDO::getTargetDeptId)
                .collect(Collectors.toList());
    }

    @Override
    public List<CrossDeptPermissionDO> getUserCrossDeptPermissions(Long userId) {
        return crossDeptPermissionMapper.selectList(
            new QueryWrapper<CrossDeptPermissionDO>()
                .eq("source_user_id", userId)
                .eq("status", 1)
                .eq("deleted", false)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setCrossDeptPermissions(Long userId, List<CrossDeptPermissionDO> permissions) {
        if (CollectionUtils.isEmpty(permissions)) {
            return;
        }
        
        // 删除原有权限
        crossDeptPermissionMapper.delete(
            new QueryWrapper<CrossDeptPermissionDO>()
                .eq("source_user_id", userId)
        );
        
        // 插入新权限
        for (CrossDeptPermissionDO permission : permissions) {
            permission.setSourceUserId(userId);
            permission.setStatus(1);
            permission.setDeleted(false);
            crossDeptPermissionMapper.insert(permission);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCrossDeptPermissions(Long userId, String permissionType) {
        crossDeptPermissionMapper.delete(
            new QueryWrapper<CrossDeptPermissionDO>()
                .eq("source_user_id", userId)
                .eq("permission_type", permissionType)
        );
    }
} 