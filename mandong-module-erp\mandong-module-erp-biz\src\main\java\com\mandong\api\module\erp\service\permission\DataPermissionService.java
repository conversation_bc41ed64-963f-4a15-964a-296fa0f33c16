package com.mandong.api.module.erp.service.permission;

import com.mandong.api.module.erp.controller.admin.order.vo.OrderPageReqVO;
import com.mandong.api.module.erp.dto.permission.OrderPermissionDTO;
import com.mandong.api.module.erp.dto.permission.UserHierarchyDTO;

import java.util.List;

/**
 * 数据权限服务接口
 */
public interface DataPermissionService {
    
    /**
     * 获取用户的订单查询权限
     */
    OrderPermissionDTO getUserOrderPermission(Long userId);
    
    /**
     * 清除用户权限缓存
     * 
     * @param userId 用户ID
     */
    void evictUserPermissionCache(Long userId);

    /**
     * 清除所有用户权限缓存
     */
    void evictAllUserPermissionCache();
    
    /**
     * 获取用户的层级信息
     */
    UserHierarchyDTO getUserHierarchyInfo(Long userId);

    /**
     * 检查用户是否有管理指定部门的跨部门权限
     */
    boolean hasCrossDeptPermission(Long userId, Long targetDeptId, String permissionType);

    /**
     * 检查用户是否有带队管理权限（包括同部门和跨部门）
     */
    boolean hasLeadGroupPermission(Long userId, Long targetDeptId);

    /**
     * 检查用户是否有运维组管理权限（包括同部门和跨部门）
     */
    boolean hasOptGroupPermission(Long userId, Long targetDeptId);

    /**
     * 获取用户可管理的部门列表（包括同部门和跨部门）
     */
    List<Long> getManageableDeptIds(Long userId, String permissionType);
    
    /**
     * 获取用户可访问的产品ID列表
     */
    List<Long> getUserAccessibleProductIds(Long userId);
    
    /**
     * 生成权限SQL条件
     */
    String generatePermissionSql(Long userId, String tableAlias, OrderPageReqVO pageReqVO);
    
    /**
     * 检查用户是否有权限查看指定订单
     */
    boolean canViewOrder(Long userId, Long productId, String channelCode, String serverName);
    
    /**
     * 检查用户是否有权限查看指定运维组的数据
     */
    boolean canViewOptData(Long userId, Long optId);
    
    /**
     * 检查用户是否有权限管理运维组数据
     */
    boolean canManageOptData(Long userId);
    
    /**
     * 检查用户是否有权限查看指定带队组的数据
     */
    boolean canViewLeadGroupData(Long userId, Long leadGroupId);
    
    /**
     * 检查用户是否有权限管理带队组数据
     */
    boolean canManageLeadGroupData(Long userId);
} 