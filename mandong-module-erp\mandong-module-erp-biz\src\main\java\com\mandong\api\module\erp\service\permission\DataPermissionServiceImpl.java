package com.mandong.api.module.erp.service.permission;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.controller.admin.order.vo.OrderPageReqVO;
import com.mandong.api.module.erp.dal.dataobject.department.DepartmentDO;
import com.mandong.api.module.erp.dal.dataobject.department.UserDeptRelationDO;
import com.mandong.api.module.erp.dal.dataobject.permission.UserPermissionMappingDO;
import com.mandong.api.module.erp.dal.sdkMysql.department.DepartmentMapper;
import com.mandong.api.module.erp.dal.sdkMysql.department.UserDeptRelationMapper;
import com.mandong.api.module.erp.dal.sdkMysql.permission.UserPermissionMappingMapper;
import com.mandong.api.module.erp.dto.permission.OrderPermissionDTO;
import com.mandong.api.module.erp.dto.permission.UserHierarchyDTO;
import com.mandong.api.module.erp.enums.RoleTypeEnum;
import com.mandong.api.module.erp.service.resourcebinding.UserResourceBindingService;
import com.mandong.api.module.system.api.permission.PermissionApi;
import com.mandong.api.module.system.enums.permission.RoleCodeEnum;
import com.taobao.api.internal.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@DS("sdkDB")
public class DataPermissionServiceImpl implements DataPermissionService {

    @Resource
    private UserDeptRelationMapper userDeptRelationMapper;
    
    @Resource
    private DepartmentMapper departmentMapper;
    
    @Resource
    private UserPermissionMappingMapper userPermissionMappingMapper;
    
    @Resource
    private PermissionApi permissionApi;

    @Resource
    private CrossDeptPermissionService crossDeptPermissionService;

    @Resource
    private UserResourceBindingService userResourceBindingService;

    @Override
    @Cacheable(value = "permission#2m", key = "#userId")
    public OrderPermissionDTO getUserOrderPermission(Long userId) {
        log.debug("查询用户权限信息，userId: {}", userId);
        
        // 检查是否为管理员，管理员拥有所有权限
        if (isSuperAdmin(userId)) {
            return createSuperAdminPermission(userId);
        }
        
        UserHierarchyDTO userHierarchy = getUserHierarchyInfo(userId);
        
        OrderPermissionDTO permission = new OrderPermissionDTO();
        permission.setUserId(userId);
        permission.setRoleType(userHierarchy.getRoleType());
        
        // 基于绑定关系获取权限
        switch (userHierarchy.getRoleType()) {
            case SUPERVISOR:
                // 主管可以查看所有下属绑定的资源数据
                permission.setAccessibleUserIds(getAllSubordinateIds(userId));
//                permission.setAccessibleProductIds(userResourceBindingService.getUserBoundProductIds(userId));
//                permission.setAccessibleChannels(userResourceBindingService.getUserBoundChannels(userId));
//                permission.setAccessibleServers(userResourceBindingService.getUserBoundServers(userId));
                break;
                
            case TEAM_LEADER:
                // 组长可以查看本组成员绑定的资源数据
                permission.setAccessibleUserIds(getTeamMemberIds(userId));
//                permission.setAccessibleProductIds(userResourceBindingService.getUserBoundProductIds(userId));
//                permission.setAccessibleChannels(userResourceBindingService.getUserBoundChannels(userId));
//                permission.setAccessibleServers(userResourceBindingService.getUserBoundServers(userId));
                break;
                
            case TEAM_MEMBER:
                // 组员只能查看自己绑定的资源数据
                permission.setAccessibleUserIds(Collections.singletonList(userId));
//                permission.setAccessibleProductIds(userResourceBindingService.getUserBoundProductIds(userId));
//                permission.setAccessibleChannels(userResourceBindingService.getUserBoundChannels(userId));
//                permission.setAccessibleServers(userResourceBindingService.getUserBoundServers(userId));
                break;
                
            default:
                // 默认无权限
                permission.setAccessibleUserIds(Collections.emptyList());
                permission.setAccessibleProductIds(Collections.emptyList());
                permission.setAccessibleChannels(Collections.emptyList());
                permission.setAccessibleServers(Collections.emptyList());
                break;
        }
        
        return permission;
    }

    /**
     * 清除用户权限缓存
     * 
     * @param userId 用户ID
     */
    @CacheEvict(value = "permission#2m", key = "#userId")
    public void evictUserPermissionCache(Long userId) {
        log.info("清除用户权限缓存，userId: {}", userId);
    }

    /**
     * 清除所有用户权限缓存
     */
    @CacheEvict(value = "permission#2m", allEntries = true)
    public void evictAllUserPermissionCache() {
        log.info("清除所有用户权限缓存");
    }

    @Override
    public String generatePermissionSql(Long userId, String tableAlias, OrderPageReqVO pageReqVO) {
        // 管理员无需权限过滤
        if (isSuperAdmin(userId)) {
            return "1=1";
        }
        // 使用高效的子查询方式生成权限SQL，避免长OR条件导致的性能问题
        String subQuerySql = String.format(
            "EXISTS (" +
                "SELECT 1 FROM qsdk_user_resource_binding urb " +
                "WHERE 1=1 " +
                "AND urb.deleted = 0 AND urb.status = 1 " +
                    "%s " + "AND (" +
                    // 运营人员权限：产品+渠道匹配
                    "(urb.resource_type = 'PRODUCT_CHANNEL' " +
                    "AND %s.productId = urb.product_id " +
                    "AND %s.channelCode = urb.channel_code) " +
                    
                    // 运维人员权限：产品+渠道+区服匹配  
                    "OR (urb.resource_type = 'SERVER' " +
                    "AND %s.productId = urb.product_id " +
                    "AND %s.channelCode = urb.channel_code " +
                    "AND %s.serverName = urb.server_name) " +

                ")" +
            ")",
            getUserIdsCondition(pageReqVO.getUserIds()),
            tableAlias, tableAlias,  // PRODUCT_CHANNEL 条件
            tableAlias, tableAlias, tableAlias,  // SERVER 条件
            tableAlias  // OPERATION_TEAM 条件
        );
        
        return subQuerySql;
    }
    // 添加一个辅助方法来生成用户ID的IN条件
    private String getUserIdsCondition(List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return "";
        }
        return "AND urb.user_id IN (" + StringUtils.join(userIds, ",") + ")";
    }
    @Override
    public boolean canViewOrder(Long userId, Long productId, String channelCode, String serverName) {
        // 管理员可以查看所有订单
        if (isSuperAdmin(userId)) {
            return true;
        }
        
        OrderPermissionDTO permission = getUserOrderPermission(userId);
        if (permission == null) {
            return false;
        }
        
        // 检查产品权限
        if (!CollectionUtils.isEmpty(permission.getAccessibleProductIds()) && 
            !permission.getAccessibleProductIds().contains(productId)) {
            return false;
        }
        
        // 检查渠道权限
        if (!CollectionUtils.isEmpty(permission.getAccessibleChannels()) && 
            !permission.getAccessibleChannels().contains(channelCode)) {
            return false;
        }
        
        // 检查区服权限
        if (!CollectionUtils.isEmpty(permission.getAccessibleServers()) && 
            !permission.getAccessibleServers().contains(serverName)) {
            return false;
        }
        
        return true;
    }

    @Override
    public boolean canViewOptData(Long userId, Long optId) {
        // 管理员可以查看所有运维组数据
        if (isSuperAdmin(userId)) {
            return true;
        }
        
        UserHierarchyDTO hierarchy = getUserHierarchyInfo(userId);
        if (hierarchy == null) {
            return false;
        }
        
        // 主管可以查看所有数据
        if (hierarchy.getRoleType() == RoleTypeEnum.SUPERVISOR) {
            return true;
        }
        
        // 组长和组员需要检查是否属于该运维组
        // 这里需要查询用户所属的运维组信息
        // 暂时返回false，具体实现需要根据实际业务逻辑调整
        return false;
    }

    @Override
    public boolean canManageOptData(Long userId) {
        // 管理员可以管理所有运维组数据
        if (isSuperAdmin(userId)) {
            return true;
        }
        
        UserHierarchyDTO hierarchy = getUserHierarchyInfo(userId);
        if (hierarchy == null) {
            return false;
        }
        
        // 只有主管和组长可以管理运维组数据
        return hierarchy.getRoleType() == RoleTypeEnum.SUPERVISOR || 
               hierarchy.getRoleType() == RoleTypeEnum.TEAM_LEADER;
    }

    @Override
    public boolean canViewLeadGroupData(Long userId, Long leadGroupId) {
        // 管理员可以查看所有带队组数据
        if (isSuperAdmin(userId)) {
            return true;
        }
        
        UserHierarchyDTO hierarchy = getUserHierarchyInfo(userId);
        if (hierarchy == null) {
            return false;
        }
        
        // 主管可以查看所有数据
        if (hierarchy.getRoleType() == RoleTypeEnum.SUPERVISOR) {
            return true;
        }
        
        // 组长和组员需要检查是否属于该带队组
        // 这里需要查询用户所属的带队组信息
        // 暂时返回false，具体实现需要根据实际业务逻辑调整
        return false;
    }

    @Override
    public boolean canManageLeadGroupData(Long userId) {
        // 管理员可以管理所有带队组数据
        if (isSuperAdmin(userId)) {
            return true;
        }
        
        UserHierarchyDTO hierarchy = getUserHierarchyInfo(userId);
        if (hierarchy == null) {
            return false;
        }
        
        // 只有主管和组长可以管理带队组数据
        return hierarchy.getRoleType() == RoleTypeEnum.SUPERVISOR || 
               hierarchy.getRoleType() == RoleTypeEnum.TEAM_LEADER;
    }

    @Override
    public List<Long> getUserAccessibleProductIds(Long userId) {
        // 管理员返回空列表，表示无限制
        if (isSuperAdmin(userId)) {
            return new ArrayList<>();
        }
        
        // 基于绑定关系获取用户可访问的产品ID
        return userResourceBindingService.getUserBoundProductIds(userId);
    }

    @Override
    public UserHierarchyDTO getUserHierarchyInfo(Long userId) {
        // 管理员直接返回超级管理员层级
        if (isSuperAdmin(userId)) {
            UserHierarchyDTO adminHierarchy = new UserHierarchyDTO();
            adminHierarchy.setUserId(userId);
            adminHierarchy.setRoleType(RoleTypeEnum.SUPERVISOR); // 视为主管级别，但实际上是超级权限
            adminHierarchy.setDepartmentIds(Collections.emptyList());
            return adminHierarchy;
        }
        
        UserHierarchyDTO userHierarchy = new UserHierarchyDTO();
        userHierarchy.setUserId(userId);
        
        // 查询用户部门关系
        List<UserDeptRelationDO> relations = userDeptRelationMapper.selectList(
            new QueryWrapper<UserDeptRelationDO>()
                .eq("user_id", userId)
                .eq("status", 1)
                .eq("deleted", 0)
        );
        
        if (CollectionUtils.isEmpty(relations)) {
            userHierarchy.setRoleType(RoleTypeEnum.TEAM_MEMBER);
            return userHierarchy;
        }
        
        // 获取用户所在的部门
        List<Long> deptIds = relations.stream()
                .map(UserDeptRelationDO::getDeptId)
                .collect(Collectors.toList());
        
        List<DepartmentDO> departments = departmentMapper.selectBatchIds(deptIds);
        
        // 判断用户角色类型
        RoleTypeEnum roleType = determineRoleType(departments);
        userHierarchy.setRoleType(roleType);
        userHierarchy.setDepartmentIds(deptIds);
        
        return userHierarchy;
    }

    /**
     * 获取所有下属用户ID（主管用）
     * 重要：只获取当前主管所管理的部门树下的用户，确保主管之间数据隔离
     */
    private List<Long> getAllSubordinateIds(Long supervisorId) {
        // 1. 获取主管所在的部门（主管级别的部门）
        List<UserDeptRelationDO> supervisorRelations = userDeptRelationMapper.selectList(
            new QueryWrapper<UserDeptRelationDO>()
                .eq("user_id", supervisorId)
                .eq("status", 1)
        );
        
        if (CollectionUtils.isEmpty(supervisorRelations)) {
            return Collections.emptyList();
        }
        
        // 2. 只取主管级别的部门（dept_level = 1）
        List<Long> supervisorDeptIds = new ArrayList<>();
        for (UserDeptRelationDO relation : supervisorRelations) {
            DepartmentDO dept = departmentMapper.selectById(relation.getDeptId());
            if (dept != null && dept.getDeptLevel() == 1) { // 只有主管级别部门
                supervisorDeptIds.add(dept.getId());
            }
        }
        
        if (CollectionUtils.isEmpty(supervisorDeptIds)) {
            return Collections.emptyList();
        }
        
        // 3. 获取这个主管部门下的所有子部门（递归）
        List<Long> allSubDeptIds = getAllSubDepartmentIds(supervisorDeptIds);
        
        // 4. 获取所有子部门的用户，排除主管自己
        List<UserDeptRelationDO> subordinateRelations = userDeptRelationMapper.selectList(
            new QueryWrapper<UserDeptRelationDO>()
                .in("dept_id", allSubDeptIds)
                .ne("user_id", supervisorId) // 排除主管自己
                .eq("status", 1)
        );
        
        return subordinateRelations.stream()
                .map(UserDeptRelationDO::getUserId)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取组员用户ID（组长用）
     * 组长只能看到自己组内的组员数据
     */
    private List<Long> getTeamMemberIds(Long teamLeaderId) {
        // 1. 获取组长所在的部门（组长级别的部门）
        List<UserDeptRelationDO> leaderRelations = userDeptRelationMapper.selectList(
            new QueryWrapper<UserDeptRelationDO>()
                .eq("user_id", teamLeaderId)
                .eq("status", 1)
        );
        
        if (CollectionUtils.isEmpty(leaderRelations)) {
            return Collections.emptyList();
        }
        
        // 2. 只取组长级别的部门（dept_level = 2）
        List<Long> teamLeaderDeptIds = new ArrayList<>();
        for (UserDeptRelationDO relation : leaderRelations) {
            DepartmentDO dept = departmentMapper.selectById(relation.getDeptId());
            if (dept != null && dept.getDeptLevel() == 2) { // 只有组长级别部门
                teamLeaderDeptIds.add(dept.getId());
            }
        }
        
        if (CollectionUtils.isEmpty(teamLeaderDeptIds)) {
            return Collections.emptyList();
        }
        
        // 3. 获取组长部门下的所有子部门（组员部门）
        List<Long> memberDeptIds = getAllSubDepartmentIds(teamLeaderDeptIds);
        
        // 4. 获取这些部门的组员用户，排除组长自己
        List<UserDeptRelationDO> memberRelations = userDeptRelationMapper.selectList(
            new QueryWrapper<UserDeptRelationDO>()
                .in("dept_id", memberDeptIds)
                .ne("user_id", teamLeaderId) // 排除组长自己
                .eq("status", 1)
        );
        
        return memberRelations.stream()
                .map(UserDeptRelationDO::getUserId)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取所有可访问的产品ID（主管用）
     * 基于主管管理的用户范围，确保主管之间数据隔离
     */
    private List<Long> getAllAccessibleProductIds(Long supervisorId) {
        // 1. 获取主管自己的产品权限
        List<Long> supervisorProductIds = getUserAccessibleProductIds(supervisorId);
        
        // 2. 获取主管下属的产品权限
        List<Long> subordinateIds = getAllSubordinateIds(supervisorId);
        
        List<Long> allAccessibleProductIds = new ArrayList<>(supervisorProductIds);
        
        if (!CollectionUtils.isEmpty(subordinateIds)) {
            List<UserPermissionMappingDO> subordinateMappings = userPermissionMappingMapper.selectList(
                new QueryWrapper<UserPermissionMappingDO>()
                    .in("user_id", subordinateIds)
                    .eq("resource_type", "PRODUCT")
                    .eq("status", 1)
            );
            
            List<Long> subordinateProductIds = subordinateMappings.stream()
                    .map(mapping -> Long.valueOf(mapping.getResourceId()))
                    .collect(Collectors.toList());
            
            allAccessibleProductIds.addAll(subordinateProductIds);
        }
        
        return allAccessibleProductIds.stream()
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取组可访问的产品ID（组长用）
     * 基于组长管理的组员范围
     */
    private List<Long> getTeamAccessibleProductIds(Long teamLeaderId) {
        // 1. 获取组长自己的产品权限
        List<Long> leaderProductIds = getUserAccessibleProductIds(teamLeaderId);
        
        // 2. 获取组员的产品权限
        List<Long> memberIds = getTeamMemberIds(teamLeaderId);
        
        List<Long> allAccessibleProductIds = new ArrayList<>(leaderProductIds);
        
        if (!CollectionUtils.isEmpty(memberIds)) {
            List<UserPermissionMappingDO> memberMappings = userPermissionMappingMapper.selectList(
                new QueryWrapper<UserPermissionMappingDO>()
                    .in("user_id", memberIds)
                    .eq("resource_type", "PRODUCT")
                    .eq("status", 1)
            );
            
            List<Long> memberProductIds = memberMappings.stream()
                    .map(mapping -> Long.valueOf(mapping.getResourceId()))
                    .collect(Collectors.toList());
            
            allAccessibleProductIds.addAll(memberProductIds);
        }
        
        return allAccessibleProductIds.stream()
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取所有可访问的渠道（主管用）
     * 基于主管管理的用户范围，确保主管之间数据隔离
     */
    private List<String> getAllAccessibleChannels(Long supervisorId) {
        // 1. 获取主管自己的渠道权限
        List<String> supervisorChannels = getUserAccessibleChannels(supervisorId);
        
        // 2. 获取主管下属的渠道权限
        List<Long> subordinateIds = getAllSubordinateIds(supervisorId);
        
        List<String> allAccessibleChannels = new ArrayList<>(supervisorChannels);
        
        if (!CollectionUtils.isEmpty(subordinateIds)) {
            List<UserPermissionMappingDO> subordinateMappings = userPermissionMappingMapper.selectList(
                new QueryWrapper<UserPermissionMappingDO>()
                    .in("user_id", subordinateIds)
                    .eq("resource_type", "CHANNEL")
                    .eq("status", 1)
            );
            
            List<String> subordinateChannels = subordinateMappings.stream()
                    .map(UserPermissionMappingDO::getResourceId)
                    .collect(Collectors.toList());
            
            allAccessibleChannels.addAll(subordinateChannels);
        }
        
        return allAccessibleChannels.stream()
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取组可访问的渠道（组长用）
     * 基于组长管理的组员范围
     */
    private List<String> getTeamAccessibleChannels(Long teamLeaderId) {
        // 1. 获取组长自己的渠道权限
        List<String> leaderChannels = getUserAccessibleChannels(teamLeaderId);
        
        // 2. 获取组员的渠道权限
        List<Long> memberIds = getTeamMemberIds(teamLeaderId);
        
        List<String> allAccessibleChannels = new ArrayList<>(leaderChannels);
        
        if (!CollectionUtils.isEmpty(memberIds)) {
            List<UserPermissionMappingDO> memberMappings = userPermissionMappingMapper.selectList(
                new QueryWrapper<UserPermissionMappingDO>()
                    .in("user_id", memberIds)
                    .eq("resource_type", "CHANNEL")
                    .eq("status", 1)
            );
            
            List<String> memberChannels = memberMappings.stream()
                    .map(UserPermissionMappingDO::getResourceId)
                    .collect(Collectors.toList());
            
            allAccessibleChannels.addAll(memberChannels);
        }
        
        return allAccessibleChannels.stream()
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取所有可访问的区服（主管用）
     * 基于主管管理的用户范围，确保主管之间数据隔离
     */
    private List<String> getAllAccessibleServers(Long supervisorId) {
        // 1. 获取主管自己的区服权限
        List<String> supervisorServers = getUserAccessibleServers(supervisorId);
        
        // 2. 获取主管下属的区服权限
        List<Long> subordinateIds = getAllSubordinateIds(supervisorId);
        
        List<String> allAccessibleServers = new ArrayList<>(supervisorServers);
        
        if (!CollectionUtils.isEmpty(subordinateIds)) {
            List<UserPermissionMappingDO> subordinateMappings = userPermissionMappingMapper.selectList(
                new QueryWrapper<UserPermissionMappingDO>()
                    .in("user_id", subordinateIds)
                    .eq("resource_type", "SERVER")
                    .eq("status", 1)
            );
            
            List<String> subordinateServers = subordinateMappings.stream()
                    .map(UserPermissionMappingDO::getResourceId)
                    .collect(Collectors.toList());
            
            allAccessibleServers.addAll(subordinateServers);
        }
        
        return allAccessibleServers.stream()
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取组可访问的区服（组长用）
     * 基于组长管理的组员范围
     */
    private List<String> getTeamAccessibleServers(Long teamLeaderId) {
        // 1. 获取组长自己的区服权限
        List<String> leaderServers = getUserAccessibleServers(teamLeaderId);
        
        // 2. 获取组员的区服权限  
        List<Long> memberIds = getTeamMemberIds(teamLeaderId);
        
        List<String> allAccessibleServers = new ArrayList<>(leaderServers);
        
        if (!CollectionUtils.isEmpty(memberIds)) {
            List<UserPermissionMappingDO> memberMappings = userPermissionMappingMapper.selectList(
                new QueryWrapper<UserPermissionMappingDO>()
                    .in("user_id", memberIds)
                    .eq("resource_type", "SERVER")
                    .eq("status", 1)
            );
            
            List<String> memberServers = memberMappings.stream()
                    .map(UserPermissionMappingDO::getResourceId)
                    .collect(Collectors.toList());
            
            allAccessibleServers.addAll(memberServers);
        }
        
        return allAccessibleServers.stream()
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取用户可访问的渠道（组员用）
     */
    private List<String> getUserAccessibleChannels(Long userId) {
        List<UserPermissionMappingDO> mappings = userPermissionMappingMapper.selectList(
            new QueryWrapper<UserPermissionMappingDO>()
                .eq("user_id", userId)
                .eq("resource_type", "CHANNEL")
                .eq("status", 1)
        );
        
        return mappings.stream()
                .map(UserPermissionMappingDO::getResourceId)
                .collect(Collectors.toList());
    }

    /**
     * 获取用户可访问的区服（组员用）
     */
    private List<String> getUserAccessibleServers(Long userId) {
        List<UserPermissionMappingDO> mappings = userPermissionMappingMapper.selectList(
            new QueryWrapper<UserPermissionMappingDO>()
                .eq("user_id", userId)
                .eq("resource_type", "SERVER")
                .eq("status", 1)
        );
        
        return mappings.stream()
                .map(UserPermissionMappingDO::getResourceId)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有子部门ID
     */
    private List<Long> getAllSubDepartmentIds(List<Long> parentIds) {
        List<DepartmentDO> subDepartments = departmentMapper.selectList(
            new QueryWrapper<DepartmentDO>()
                .in("parent_id", parentIds)
                .eq("status", 1)
        );
        
        List<Long> allSubIds = new ArrayList<>(parentIds);
        
        if (!CollectionUtils.isEmpty(subDepartments)) {
            List<Long> directSubIds = subDepartments.stream()
                    .map(DepartmentDO::getId)
                    .collect(Collectors.toList());
            
            allSubIds.addAll(directSubIds);
            
            // 递归获取子部门的子部门
            List<Long> deepSubIds = getAllSubDepartmentIds(directSubIds);
            allSubIds.addAll(deepSubIds);
        }
        
        return allSubIds.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 根据部门信息判断用户角色类型
     */
    private RoleTypeEnum determineRoleType(List<DepartmentDO> departments) {
        if (CollectionUtils.isEmpty(departments)) {
            return RoleTypeEnum.TEAM_MEMBER;
        }
        
        // 如果有主管级别的部门，则是主管
        boolean hasSupervisorDept = departments.stream()
                .anyMatch(dept -> dept.getDeptLevel() == 1);
        
        if (hasSupervisorDept) {
            return RoleTypeEnum.SUPERVISOR;
        }
        
        // 如果有组长级别的部门，则是组长
        boolean hasTeamLeaderDept = departments.stream()
                .anyMatch(dept -> dept.getDeptLevel() == 2);
        
        if (hasTeamLeaderDept) {
            return RoleTypeEnum.TEAM_LEADER;
        }
        
        // 默认为组员
        return RoleTypeEnum.TEAM_MEMBER;
    }

    /**
     * 检查用户是否为超级管理员
     */
    private boolean isSuperAdmin(Long userId) {
        try {
            // 方法1：通过用户ID判断（通常管理员用户ID为1）
            if (userId.equals(1L)) {
                return true;
            }
            
            // 方法2：通过角色判断
            if (permissionApi != null) {
                return permissionApi.hasAnyRoles(userId, RoleCodeEnum.SUPER_ADMIN.getCode());
            }

            
        } catch (Exception e) {
            log.warn("检查管理员权限时发生异常: userId={}, error={}", userId, e.getMessage());
        }
        
        return false;
    }

    /**
     * 创建超级管理员权限对象
     */
    private OrderPermissionDTO createSuperAdminPermission(Long userId) {
        OrderPermissionDTO permission = new OrderPermissionDTO();
        permission.setUserId(userId);
        permission.setRoleType(RoleTypeEnum.SUPERVISOR);
        permission.setHasAllPermission(true); // 标记拥有所有权限
        
        // 空列表表示无限制
        permission.setAccessibleUserIds(new ArrayList<>());
        permission.setAccessibleProductIds(new ArrayList<>());
        permission.setAccessibleChannels(new ArrayList<>());
        permission.setAccessibleServers(new ArrayList<>());
        
        return permission;
    }

    @Override
    public boolean hasCrossDeptPermission(Long userId, Long targetDeptId, String permissionType) {
        // 管理员拥有所有权限
        if (isSuperAdmin(userId)) {
            return true;
        }
        
        return crossDeptPermissionService.hasManagePermission(userId, targetDeptId, permissionType);
    }

    @Override
    public boolean hasLeadGroupPermission(Long userId, Long targetDeptId) {
        // 管理员拥有所有权限
        if (isSuperAdmin(userId)) {
            return true;
        }
        
        // 检查用户是否在目标部门内（同部门权限）
        UserHierarchyDTO hierarchy = getUserHierarchyInfo(userId);
        if (hierarchy != null && hierarchy.getDepartmentIds() != null) {
            if (hierarchy.getDepartmentIds().contains(targetDeptId)) {
                // 同部门内，主管和组长有管理权限
                return hierarchy.getRoleType() == RoleTypeEnum.SUPERVISOR || 
                       hierarchy.getRoleType() == RoleTypeEnum.TEAM_LEADER;
            }
        }
        
        // 检查跨部门权限
        return crossDeptPermissionService.hasLeadGroupManagePermission(userId, targetDeptId);
    }

    @Override
    public boolean hasOptGroupPermission(Long userId, Long targetDeptId) {
        // 管理员拥有所有权限
        if (isSuperAdmin(userId)) {
            return true;
        }
        
        // 检查用户是否在目标部门内（同部门权限）
        UserHierarchyDTO hierarchy = getUserHierarchyInfo(userId);
        if (hierarchy != null && hierarchy.getDepartmentIds() != null) {
            if (hierarchy.getDepartmentIds().contains(targetDeptId)) {
                // 同部门内，主管和组长有管理权限
                return hierarchy.getRoleType() == RoleTypeEnum.SUPERVISOR || 
                       hierarchy.getRoleType() == RoleTypeEnum.TEAM_LEADER;
            }
        }
        
        // 检查跨部门权限
        return crossDeptPermissionService.hasOptGroupManagePermission(userId, targetDeptId);
    }

    @Override
    public List<Long> getManageableDeptIds(Long userId, String permissionType) {
        // 管理员可以管理所有部门
        if (isSuperAdmin(userId)) {
            return new ArrayList<>(); // 空列表表示无限制
        }
        
        List<Long> manageableDeptIds = new ArrayList<>();
        
        // 1. 获取用户在同部门内的管理权限
        UserHierarchyDTO hierarchy = getUserHierarchyInfo(userId);
        if (hierarchy != null && hierarchy.getDepartmentIds() != null) {
            // 主管和组长可以管理自己所在的部门
            if (hierarchy.getRoleType() == RoleTypeEnum.SUPERVISOR || 
                hierarchy.getRoleType() == RoleTypeEnum.TEAM_LEADER) {
                manageableDeptIds.addAll(hierarchy.getDepartmentIds());
            }
        }
        
        // 2. 获取跨部门权限
        List<Long> crossDeptIds = crossDeptPermissionService.getManageableDeptIds(userId, permissionType);
        manageableDeptIds.addAll(crossDeptIds);
        
        return manageableDeptIds.stream().distinct().collect(Collectors.toList());
    }
} 