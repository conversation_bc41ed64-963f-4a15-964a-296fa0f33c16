package com.mandong.api.module.erp.service.report;

import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderSummaryReqVO;
import com.mandong.api.module.erp.controller.admin.report.vo.OptSalePriceVO;
import com.mandong.api.module.erp.dal.sdkMysql.opt.SdkOptLinkMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.List;

@Service
public class ReportServiceImpl implements ReportService {

    @Resource
    private SdkOptLinkMapper sdkOptLinkMapper;

    @Override
    public List<OptSalePriceVO> GetOptSalePrice(SdkOrderSummaryReqVO reqVO) {
        if (reqVO.getPayTime() == null || reqVO.getPayTime().length == 0) {
            // 计算今天的时间戳，例如 2020-01-01 00:00:00 到 2020-01-01 23:59:59
            LocalDate today = LocalDate.now();

            // 今天的开始时间：00:00:00
            LocalDateTime startOfDay = LocalDateTime.of(today, LocalTime.MIN);
            long startTimestamp = startOfDay.atZone(ZoneId.systemDefault()).toEpochSecond();

            // 今天的结束时间：23:59:59
            LocalDateTime endOfDay = LocalDateTime.of(today, LocalTime.MAX);
            long endTimestamp = endOfDay.atZone(ZoneId.systemDefault()).toEpochSecond();

            // 设置今天的时间范围
            reqVO.setPayTime(new Long[]{startTimestamp, endTimestamp});
        }

        // 使用设置好的时间范围调用 mapper 方法
        Long[] payTime = reqVO.getPayTime();
        return sdkOptLinkMapper.getReportOptSalePrice(payTime[0], payTime[1]);
    }
}
