package com.mandong.api.module.erp.service.resourcebinding;

import com.mandong.api.module.erp.controller.admin.resourcebinding.vo.UserResourceBindingVO;
import com.mandong.api.module.erp.controller.admin.resourcebinding.vo.BatchResourceBindingReqVO;
import com.mandong.api.module.erp.dal.dataobject.resourcebinding.UserResourceBindingDO;

import java.util.List;

/**
 * 用户资源绑定 Service 接口
 */
public interface UserResourceBindingService {

    /**
     * 获取用户资源绑定列表
     */
    List<UserResourceBindingVO> getUserResourceBinding(Long userId);

    /**
     * 批量设置用户资源绑定
     */
    void batchSetUserResourceBinding(BatchResourceBindingReqVO reqVO);

    /**
     * 删除用户资源绑定
     */
    void deleteUserResourceBinding(Long id);

    /**
     * 获取用户可访问的资源绑定
     */
    List<UserResourceBindingVO> getAccessibleBindings(Long userId);

    /**
     * 测试用户权限
     */
    boolean testUserPermission(Long userId, String resourceType, String resourceValue);

    /**
     * 获取用户可访问的产品ID列表
     */
    List<Long> getUserBoundProductIds(Long userId);

    /**
     * 获取用户可访问的渠道代码列表
     */
    List<String> getUserBoundChannelCodes(Long userId);

    /**
     * 获取用户可访问的运营队伍列表
     */
    List<String> getUserBoundOperationTeams(Long userId);

    /**
     * 获取用户可访问的区服列表
     */
    List<String> getUserBoundServerNames(Long userId);

    /**
     * 检查用户对指定产品的访问权限
     */
    boolean hasProductAccess(Long userId, Long productId);

    /**
     * 检查用户对指定渠道的访问权限
     */
    boolean hasChannelAccess(Long userId, String channelCode);

    /**
     * 检查用户对指定运营队伍的访问权限
     */
    boolean hasOperationTeamAccess(Long userId, String operationTeam);

    /**
     * 检查用户对指定区服的访问权限
     */
    boolean hasServerAccess(Long userId, String serverName);

    /**
     * 获取用户可访问的渠道列表（为兼容DataPermissionServiceImpl）
     */
    List<String> getUserBoundChannels(Long userId);

    /**
     * 获取用户可访问的区服列表（为兼容DataPermissionServiceImpl）
     */
    List<String> getUserBoundServers(Long userId);

    /**
     * 获取用户的完整权限绑定组合（用于生成精确权限SQL）
     */
    List<UserResourceBindingVO> getUserPermissionCombinations(Long userId);
} 