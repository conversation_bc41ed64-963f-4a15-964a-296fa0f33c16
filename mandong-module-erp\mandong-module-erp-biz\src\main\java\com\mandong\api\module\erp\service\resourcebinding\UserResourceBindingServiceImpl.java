package com.mandong.api.module.erp.service.resourcebinding;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mandong.api.module.erp.controller.admin.resourcebinding.vo.BatchResourceBindingReqVO;
import com.mandong.api.module.erp.controller.admin.resourcebinding.vo.UserResourceBindingVO;
import com.mandong.api.module.erp.dal.dataobject.resourcebinding.UserResourceBindingDO;
import com.mandong.api.module.erp.dal.mysql.resourcebinding.UserResourceBindingMapper;
import com.mandong.api.module.erp.service.resourcebinding.UserResourceBindingService;
import com.mandong.api.module.erp.service.permission.DataPermissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户资源绑定 Service 实现类
 */
@Service
@Slf4j
public class UserResourceBindingServiceImpl implements UserResourceBindingService {

    @Resource
    private UserResourceBindingMapper userResourceBindingMapper;
    
    @Resource
    private DataPermissionService dataPermissionService;

    @Override
    @DS("sdkDB")
    public List<UserResourceBindingVO> getUserResourceBinding(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }

        List<UserResourceBindingDO> bindings = userResourceBindingMapper.selectByUserId(userId);
        return bindings.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    @DS("sdkDB")
    @Transactional(rollbackFor = Exception.class)
    public void batchSetUserResourceBinding(BatchResourceBindingReqVO reqVO) {
        if (reqVO.getUserId() == null) {
            log.error("用户ID不能为空");
            return;
        }

        String operationMode = reqVO.getOperationMode() != null ? reqVO.getOperationMode() : "REPLACE";
        log.info("批量设置用户资源绑定: userId={}, operationMode={}, bindingCount={}", 
                reqVO.getUserId(), operationMode, reqVO.getBindings().size());

        try {
            switch (operationMode) {
                case "REPLACE":
                    handleReplaceMode(reqVO);
                    break;
                case "ADD":
                    handleAddMode(reqVO);
                    break;
                case "UPDATE":
                    handleUpdateMode(reqVO);
                    break;
                case "DELETE":
                    handleDeleteMode(reqVO);
                    break;
                default:
                    log.warn("未知的操作模式: {}, 使用默认的REPLACE模式", operationMode);
                    handleReplaceMode(reqVO);
                    break;
            }
            
            // 权限变更后清除用户权限缓存
            dataPermissionService.evictUserPermissionCache(reqVO.getUserId());
            log.info("已清除用户权限缓存: userId={}", reqVO.getUserId());
            
        } catch (Exception e) {
            log.error("批量设置用户资源绑定失败: userId={}, operationMode={}", 
                    reqVO.getUserId(), operationMode, e);
            throw e;
        }
    }

    /**
     * 处理替换模式 - 删除所有现有绑定，然后添加新的绑定
     */
    private void handleReplaceMode(BatchResourceBindingReqVO reqVO) {
        log.info("执行替换模式: 先删除用户所有绑定，再添加新绑定");
        
        // 先删除用户所有的资源绑定
        userResourceBindingMapper.deleteByUserId(reqVO.getUserId());

        // 添加新的资源绑定
        addNewBindings(reqVO);
    }

    /**
     * 处理增量添加模式 - 只添加新的绑定，不删除现有的
     */
    private void handleAddMode(BatchResourceBindingReqVO reqVO) {
        log.info("执行增量添加模式: 只添加新绑定，保留现有绑定");
        
        // 直接添加新的资源绑定
        addNewBindings(reqVO);
    }

    /**
     * 处理更新模式 - 更新指定ID的绑定
     */
    private void handleUpdateMode(BatchResourceBindingReqVO reqVO) {
        log.info("执行更新模式: 更新指定的绑定记录");
        
        if (!CollectionUtils.isEmpty(reqVO.getBindings())) {
            for (BatchResourceBindingReqVO.ResourceBindingItemVO item : reqVO.getBindings()) {
                if (item.getId() != null) {
                    // 更新现有记录
                    List<UserResourceBindingDO> itemBindings = expandBindingItem(reqVO.getUserId(), item);
                    if (!itemBindings.isEmpty()) {
                        UserResourceBindingDO bindingToUpdate = itemBindings.get(0);
                        bindingToUpdate.setId(item.getId());
                        userResourceBindingMapper.updateById(bindingToUpdate);
                        log.debug("更新资源绑定: id={}, userId={}, resourceType={}", 
                                item.getId(), reqVO.getUserId(), bindingToUpdate.getResourceType());
                    }
                } else {
                    log.warn("更新模式下绑定项缺少ID: {}", item);
                }
            }
        }
    }

    /**
     * 处理删除模式 - 删除指定ID的绑定
     */
    private void handleDeleteMode(BatchResourceBindingReqVO reqVO) {
        log.info("执行删除模式: 删除指定的绑定记录");
        
        if (!CollectionUtils.isEmpty(reqVO.getBindings())) {
            for (BatchResourceBindingReqVO.ResourceBindingItemVO item : reqVO.getBindings()) {
                if (item.getId() != null) {
                    userResourceBindingMapper.deleteById(item.getId());
                    log.debug("删除资源绑定: id={}, userId={}", item.getId(), reqVO.getUserId());
                } else {
                    log.warn("删除模式下绑定项缺少ID: {}", item);
                }
            }
        }
    }

    /**
     * 添加新的绑定记录
     */
    private void addNewBindings(BatchResourceBindingReqVO reqVO) {
        if (!CollectionUtils.isEmpty(reqVO.getBindings())) {
            List<UserResourceBindingDO> allBindings = new ArrayList<>();
            
            for (BatchResourceBindingReqVO.ResourceBindingItemVO item : reqVO.getBindings()) {
                List<UserResourceBindingDO> itemBindings = expandBindingItem(reqVO.getUserId(), item);
                allBindings.addAll(itemBindings);
            }

            // 批量插入所有绑定记录
            if (!allBindings.isEmpty()) {
                allBindings.forEach(binding -> {
                    log.debug("插入资源绑定: userId={}, resourceType={}, productId={}, channelCode={}, operationTeam={}, serverName={}", 
                        binding.getUserId(), binding.getResourceType(), binding.getProductId(), 
                        binding.getChannelCode(), binding.getOperationTeam(), binding.getServerName());
                    userResourceBindingMapper.insert(binding);
                });
                log.info("批量添加用户资源绑定成功: userId={}, count={}", reqVO.getUserId(), allBindings.size());
            }
        }
    }

    @Override
    @DS("sdkDB")
    @Transactional(rollbackFor = Exception.class)
    public void deleteUserResourceBinding(Long id) {
        if (id != null) {
            // 先获取要删除的绑定信息，用于清除缓存
            UserResourceBindingDO binding = userResourceBindingMapper.selectById(id);
            if (binding != null) {
                Long userId = binding.getUserId();
                userResourceBindingMapper.deleteById(id);
                
                // 权限变更后清除用户权限缓存
                dataPermissionService.evictUserPermissionCache(userId);
                log.info("已清除用户权限缓存: userId={}", userId);
            }
        }
    }

    @Override
    @DS("sdkDB")
    public List<UserResourceBindingVO> getAccessibleBindings(Long userId) {
        return getUserResourceBinding(userId);
    }

    @Override
    @DS("sdkDB")
    public boolean testUserPermission(Long userId, String resourceType, String resourceValue) {
        if (userId == null || !StringUtils.hasText(resourceType) || !StringUtils.hasText(resourceValue)) {
            return false;
        }

        List<UserResourceBindingDO> bindings = userResourceBindingMapper.selectByUserIdAndResourceType(userId, resourceType);
        
        if (CollectionUtils.isEmpty(bindings)) {
            return false;
        }

        switch (resourceType) {
            case "PRODUCT_CHANNEL":
                return bindings.stream().anyMatch(b -> 
                    resourceValue.equals(String.valueOf(b.getProductId())) || 
                    resourceValue.equals(b.getChannelCode()));
            case "OPERATION_TEAM":
                return bindings.stream().anyMatch(b -> resourceValue.equals(b.getOperationTeam()));
            case "SERVER":
                return bindings.stream().anyMatch(b -> resourceValue.equals(b.getServerName()));
            default:
                return false;
        }
    }

    @Override
    @DS("sdkDB")
    public List<Long> getUserBoundProductIds(Long userId) {
        return userResourceBindingMapper.selectAccessibleProductIds(userId);
    }

    @Override
    @DS("sdkDB")
    public List<String> getUserBoundChannelCodes(Long userId) {
        return userResourceBindingMapper.selectAccessibleChannelCodes(userId);
    }

    @Override
    @DS("sdkDB")
    public List<String> getUserBoundOperationTeams(Long userId) {
        return userResourceBindingMapper.selectAccessibleOperationTeams(userId);
    }

    @Override
    @DS("sdkDB")
    public List<String> getUserBoundServerNames(Long userId) {
        return userResourceBindingMapper.selectAccessibleServerNames(userId);
    }

    @Override
    @DS("sdkDB")
    public boolean hasProductAccess(Long userId, Long productId) {
        if (userId == null || productId == null) {
            return false;
        }
        List<Long> productIds = getUserBoundProductIds(userId);
        return productIds.contains(productId);
    }

    @Override
    @DS("sdkDB")
    public boolean hasChannelAccess(Long userId, String channelCode) {
        if (userId == null || !StringUtils.hasText(channelCode)) {
            return false;
        }
        List<String> channelCodes = getUserBoundChannelCodes(userId);
        return channelCodes.contains(channelCode);
    }

    @Override
    @DS("sdkDB")
    public boolean hasOperationTeamAccess(Long userId, String operationTeam) {
        if (userId == null || !StringUtils.hasText(operationTeam)) {
            return false;
        }
        List<String> operationTeams = getUserBoundOperationTeams(userId);
        return operationTeams.contains(operationTeam);
    }

    @Override
    @DS("sdkDB")
    public boolean hasServerAccess(Long userId, String serverName) {
        if (userId == null || !StringUtils.hasText(serverName)) {
            return false;
        }
        List<String> serverNames = getUserBoundServerNames(userId);
        return serverNames.contains(serverName);
    }

    @Override
    @DS("sdkDB")
    public List<String> getUserBoundChannels(Long userId) {
        return getUserBoundChannelCodes(userId);
    }

    @Override
    @DS("sdkDB")
    public List<String> getUserBoundServers(Long userId) {
        return getUserBoundServerNames(userId);
    }

    @Override
    @DS("sdkDB")
    public List<UserResourceBindingVO> getUserPermissionCombinations(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        
        List<UserResourceBindingDO> bindings = userResourceBindingMapper.selectUserPermissionCombinations(userId);
        return bindings.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    /**
     * 转换为VO对象
     */
    private UserResourceBindingVO convertToVO(UserResourceBindingDO binding) {
        UserResourceBindingVO vo = new UserResourceBindingVO();
        vo.setId(binding.getId());
        vo.setUserId(binding.getUserId());
        vo.setOpsDeptId(binding.getOpsDeptId());
        vo.setResourceType(binding.getResourceType());
        vo.setTargetDeptId(binding.getTargetDeptId());
        vo.setProductId(binding.getProductId());
        vo.setChannelCode(binding.getChannelCode());
        vo.setOperationTeam(binding.getOperationTeam());
        vo.setServerName(binding.getServerName());
        vo.setStatus(binding.getStatus());
        vo.setRemark(binding.getRemark());
        vo.setCreateTime(binding.getCreateTime());
        vo.setUpdateTime(binding.getUpdateTime());
        return vo;
    }

    /**
     * 转换为DO对象
     */
    private UserResourceBindingDO convertToDO(Long userId, BatchResourceBindingReqVO.ResourceBindingItemVO item) {
        return UserResourceBindingDO.builder()
                .userId(userId)
                .opsDeptId(item.getOpsDeptId())
                .targetDeptId(item.getTargetDeptId())
                .productId(item.getProductId())
                .channelCode(item.getChannelCode())
                .operationTeam(item.getOperationTeam())
                .serverName(item.getServerName())
                .status(item.getStatus() != null ? item.getStatus() : 1)
                .remark(item.getRemark())
                .creator("system")
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .deleted(0)
                .build();
    }

    /**
     * 确定资源类型
     */
    private void determineResourceType(UserResourceBindingDO binding) {
        if (binding.getProductId() != null || StringUtils.hasText(binding.getChannelCode())) {
            binding.setResourceType("PRODUCT_CHANNEL");
        } else if (StringUtils.hasText(binding.getOperationTeam())) {
            binding.setResourceType("OPERATION_TEAM");
        } else if (StringUtils.hasText(binding.getServerName())) {
            binding.setResourceType("SERVER");
        }
    }

    /**
     * 将单个绑定项展开为多条记录
     */
    private List<UserResourceBindingDO> expandBindingItem(Long userId, BatchResourceBindingReqVO.ResourceBindingItemVO item) {
        List<UserResourceBindingDO> bindings = new ArrayList<>();

        // 处理新版数组字段：产品+渠道绑定
        if (!CollectionUtils.isEmpty(item.getSelectedProductIds()) && !CollectionUtils.isEmpty(item.getSelectedChannelCodes())) {
            // 为每个产品+渠道组合创建一条记录
            for (Long productId : item.getSelectedProductIds()) {
                for (String channelCode : item.getSelectedChannelCodes()) {
                    UserResourceBindingDO binding = createBaseBinding(userId, item);
                    binding.setResourceType("PRODUCT_CHANNEL");
                    binding.setProductId(productId);
                    binding.setChannelCode(channelCode);
                    bindings.add(binding);
                }
            }
        }
        // 只有产品ID的情况
        else if (!CollectionUtils.isEmpty(item.getSelectedProductIds())) {
            for (Long productId : item.getSelectedProductIds()) {
                UserResourceBindingDO binding = createBaseBinding(userId, item);
                binding.setResourceType("PRODUCT_CHANNEL");
                binding.setProductId(productId);
                bindings.add(binding);
            }
        }
        // 只有渠道代码的情况
        else if (!CollectionUtils.isEmpty(item.getSelectedChannelCodes())) {
            for (String channelCode : item.getSelectedChannelCodes()) {
                UserResourceBindingDO binding = createBaseBinding(userId, item);
                binding.setResourceType("PRODUCT_CHANNEL");
                binding.setChannelCode(channelCode);
                bindings.add(binding);
            }
        }

        // 处理运营队伍绑定
        if (!CollectionUtils.isEmpty(item.getOperationTeams())) {
            for (String operationTeam : item.getOperationTeams()) {
                UserResourceBindingDO binding = createBaseBinding(userId, item);
                binding.setResourceType("OPERATION_TEAM");
                binding.setOperationTeam(operationTeam);
                bindings.add(binding);
            }
        }

        // 处理区服绑定
        if (!CollectionUtils.isEmpty(item.getServerNames())) {
            for (String serverName : item.getServerNames()) {
                UserResourceBindingDO binding = createBaseBinding(userId, item);
                binding.setResourceType("SERVER");
                binding.setServerName(serverName);
                bindings.add(binding);
            }
        }

        // 兼容旧版单字段方式
        if (bindings.isEmpty()) {
            UserResourceBindingDO binding = convertToDO(userId, item);
            determineResourceType(binding);
            if (binding.getResourceType() != null) {
                bindings.add(binding);
            }
        }

        return bindings;
    }

    /**
     * 创建基础绑定对象
     */
    private UserResourceBindingDO createBaseBinding(Long userId, BatchResourceBindingReqVO.ResourceBindingItemVO item) {
        return UserResourceBindingDO.builder()
                .userId(userId)
                .opsDeptId(item.getOpsDeptId())
                .targetDeptId(item.getTargetDeptId())
                .status(item.getStatus() != null ? item.getStatus() : 1)
                .remark(item.getRemark())
                .creator("system")
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .deleted(0)
                .build();
    }
} 