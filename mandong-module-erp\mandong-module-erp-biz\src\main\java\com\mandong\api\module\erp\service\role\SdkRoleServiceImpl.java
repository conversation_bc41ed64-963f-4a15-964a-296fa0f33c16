package com.mandong.api.module.erp.service.role;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.module.erp.controller.admin.role.vo.RolePageReqVO;
import com.mandong.api.module.erp.controller.admin.role.vo.RolePageRespVO;
import com.mandong.api.module.erp.controller.admin.role.vo.SdkRoleCondition;
import com.mandong.api.module.erp.controller.admin.user.vo.*;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptLinkDO;
import com.mandong.api.module.erp.dal.sdkMysql.opt.SdkOptMapper;
import com.mandong.api.module.erp.dal.sdkMysql.role.SdkRoleMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserDeviceMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserLoginLogsMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserMapper;
import com.mandong.api.module.erp.dto.permission.OrderPermissionDTO;
import com.mandong.api.module.erp.service.permission.DataPermissionService;
import cn.hutool.core.collection.CollUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.mandong.api.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.USER_ROLE_NOT_EXISTS;

/**
 * 游戏角色 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SdkRoleServiceImpl implements SdkRoleService {

    @Resource
    private SdkRoleMapper sdkRoleMapper;
    
    @Resource
    private SdkOptMapper sdkOptMapper;
    
    @Resource
    private DataPermissionService dataPermissionService;

    @Override
    public PageResult<RolePageRespVO> getPage(RolePageReqVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String roleId = SecurityFrameworkUtils.getLoginUserRoleId();
        if (roleId == null) {
            throw exception(USER_ROLE_NOT_EXISTS);
        }
        
        // 1. 获取用户权限信息
        OrderPermissionDTO permission = dataPermissionService.getUserOrderPermission(userId);
        
        // 2. 如果是管理员，直接使用原有查询（无权限限制）
        if (permission != null && permission.getHasAllPermission() != null && permission.getHasAllPermission()) {
            return sdkRoleMapper.selectRolePage(pageReqVO);
        }
        
        // 3. 构建权限SQL条件
        List<Long> accessibleUserIds = permission != null ? permission.getAccessibleUserIds() : null;
        String permissionSql = buildPermissionRoleSql("t", accessibleUserIds);
        
        // 4. 调用支持权限SQL的查询方法
        return sdkRoleMapper.selectRolePageWithPermission(pageReqVO, permissionSql);
    }

    /**
     * 构建角色权限SQL条件（用于角色相关表查询）
     * 支持产品+渠道和产品+渠道+区服的权限匹配
     */
    private String buildPermissionRoleSql(String tableAlias, List<Long> userIds) {
        String userIdsCondition = "";
        if (CollUtil.isNotEmpty(userIds)) {
            userIdsCondition = "AND urb.user_id IN (" + CollUtil.join(userIds, ",") + ") ";
        } else {
            // 如果没有指定用户ID，则使用当前登录用户的ID
            userIdsCondition = "AND urb.user_id = " + SecurityFrameworkUtils.getLoginUserId() + " ";
        }

        return String.format(
                "EXISTS (" +
                        "SELECT 1 FROM qsdk_user_resource_binding urb " +
                        "WHERE 1=1 " +
                        "AND urb.deleted = 0 AND urb.status = 1 " +
                        "%s" + // 用户ID条件
                        "AND (" +
                        // 运营人员权限：产品+渠道匹配
                        "(urb.resource_type = 'PRODUCT_CHANNEL' " +
                        "AND %s.productId = urb.product_id " +
                        "AND %s.channelCode = urb.channel_code) " +

                        // 运维人员权限：产品+渠道+区服匹配
                        "OR (urb.resource_type = 'SERVER' " +
                        "AND %s.productId = urb.product_id " +
                        "AND %s.channelCode = urb.channel_code " +
                        "AND %s.serverName = urb.server_name) " +
                        ")" +
                        ")",
                userIdsCondition,
                tableAlias, tableAlias,  // PRODUCT_CHANNEL 条件
                tableAlias, tableAlias, tableAlias  // SERVER 条件
        );
    }
}
