package com.mandong.api.module.erp.service.user;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.github.yulichang.query.MPJLambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.mandong.api.framework.common.pojo.CommonResult;
import com.mandong.api.framework.common.pojo.PageResult;
import com.mandong.api.framework.security.core.util.SecurityFrameworkUtils;
import com.mandong.api.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.collection.CollUtil;
import com.mandong.api.module.erp.controller.admin.opt.vo.OptVO;
import com.mandong.api.module.erp.controller.admin.order.vo.SdkOrderRespVo;
import com.mandong.api.module.erp.controller.admin.user.vo.*;
import com.mandong.api.module.erp.dal.dataobject.channel.SdkChannelDO;
import com.mandong.api.module.erp.dal.dataobject.opt.SdkOptLinkDO;
import com.mandong.api.module.erp.dal.dataobject.order.SdkOrderDO;
import com.mandong.api.module.erp.dal.dataobject.user.SdkUserDO;
import com.mandong.api.module.erp.dal.sdkMysql.channel.SdkChannelMapper;
import com.mandong.api.module.erp.dal.sdkMysql.opt.SdkOptMapper;
import com.mandong.api.module.erp.dal.sdkMysql.order.SdkOrderMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserDeviceMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserLoginLogsMapper;
import com.mandong.api.module.erp.dal.sdkMysql.user.SdkUserMapper;
import com.mandong.api.module.erp.dto.permission.OrderPermissionDTO;
import com.mandong.api.module.erp.enums.ErrorCodeConstants;
import com.mandong.api.module.erp.service.permission.DataPermissionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.mandong.api.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.mandong.api.module.erp.enums.ErrorCodeConstants.USER_ROLE_NOT_EXISTS;

/**
 * 游戏用户 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class SdkUserServiceImpl implements SdkUserService {

    @Resource
    private SdkOrderMapper sdkOrderMapper;

    @Resource
    private SdkUserMapper sdkUserMapper;

    @Resource
    private SdkUserLoginLogsMapper sdkUserLoginLogsMapper;

    @Resource
    private SdkUserDeviceMapper sdkUserDeviceMapper;

    @Resource
    private SdkOptMapper sdkOptMapper;

    @Resource
    private SdkChannelMapper sdkChannelMapper;

    @Resource
    private DataPermissionService dataPermissionService;

    @Override
    public PageResult<UserPageRespVO> getUserPage(UserPageReqVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String roleId = SecurityFrameworkUtils.getLoginUserRoleId();
        if (roleId == null) {
            throw exception(USER_ROLE_NOT_EXISTS);
        }

        // 1. 获取用户权限信息
        OrderPermissionDTO permission = dataPermissionService.getUserOrderPermission(userId);
        
        // 2. 如果是管理员，直接使用原有查询（无权限限制）
        if (permission != null && permission.getHasAllPermission() != null && permission.getHasAllPermission()) {
            return sdkUserMapper.selectPage(pageReqVO);
        }
        
        // 3. 构建权限SQL条件
        List<Long> accessibleUserIds = permission != null ? permission.getAccessibleUserIds() : null;
        String permissionSql = buildPermissionUserSql("t", accessibleUserIds);

        // 4. 调用支持权限SQL的查询方法
        return sdkUserMapper.selectPageWithPermission(pageReqVO, permissionSql);
    }

    @Override
    public UserDetailRespVO getUserDetail(Long id) {
        return sdkUserMapper.getUserDetail(id);
    }

    @Override
    public PageResult<UserDevicePageRespVO> getUserDevicePage(UserPageReqVO pageReqVO) {
        return sdkUserDeviceMapper.selectUserDevicePage(pageReqVO);
    }

    @Override
    public PageResult<UserLoginPageRespVO> getUserLoginPage(UserPageReqVO pageReqVO) {
        return sdkUserLoginLogsMapper.selectUserLoginPage(pageReqVO);
    }

    @Override
    @DS("sdkDB")
    @DSTransactional(rollbackFor = Exception.class)
    public CommonResult transferChannel(SdkUserTransferReqVO reqVO) {
       try {
           SdkUserDO sdkUserDO = sdkUserMapper.selectById(reqVO.getUid());
           if (sdkUserDO == null) {
               return CommonResult.error(ErrorCodeConstants.USER_NOT_EXISTS);
           }
           sdkUserDO.setChannelCode(reqVO.getChannelCode());
           sdkUserDO.setProductId(reqVO.getProductId());
           sdkUserMapper.updateById(sdkUserDO);

           if (reqVO.getTransferRule() == 2){
               List<SdkOrderDO> SdkOrderDOs;
               if (reqVO.getTransferTime() != null){
                   SdkOrderDOs = sdkOrderMapper.selectList(new MPJLambdaQueryWrapper<SdkOrderDO>().selectAll(SdkOrderDO.class)
                           .eq(SdkOrderDO::getUid, reqVO.getUid())
                           .ge(SdkOrderDO::getCreateTime, reqVO.getTransferTime())
                   );

               }else {
                   SdkOrderDOs = sdkOrderMapper.selectList(SdkOrderDO::getUid, reqVO.getUid());
               }
               SdkOrderDOs.forEach(sdkOrderDO -> {
                   sdkOrderDO.setChannelCode(reqVO.getChannelCode());
                   sdkOrderDO.setProductId(reqVO.getProductId());
                   sdkOrderMapper.updateById(sdkOrderDO);
               });
           }
           return CommonResult.success(null);
       }catch (Exception e){
           log.error("转移渠道失败", e);
           return CommonResult.error(GlobalErrorCodeConstants.UNKNOWN);
       }
    }

    @Override
    public void updateStatus(Long uid, Integer status) {
        SdkUserDO sdkUserDO = sdkUserMapper.selectById(uid);
        if (sdkUserDO == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }
        // 移除setStatus调用，因为SdkUserDO可能没有这个字段
        // sdkUserDO.setStatus(status);
        sdkUserMapper.updateById(sdkUserDO);
    }

    /**
     * 构建用户权限SQL条件（用于用户相关表查询）
     * 支持产品+渠道和产品+渠道+区服的权限匹配
     */
    private String buildPermissionUserSql(String tableAlias, List<Long> userIds) {
        String userIdsCondition = "";
        if (CollUtil.isNotEmpty(userIds)) {
            userIdsCondition = "AND urb.user_id IN (" + CollUtil.join(userIds, ",") + ") ";
        } else {
            // 如果没有指定用户ID，则使用当前登录用户的ID
            userIdsCondition = "AND urb.user_id = " + SecurityFrameworkUtils.getLoginUserId() + " ";
        }

        return String.format(
                "EXISTS (" +
                        "SELECT 1 FROM qsdk_user_resource_binding urb " +
                        "WHERE 1=1 " +
                        "AND urb.deleted = 0 AND urb.status = 1 " +
                        "%s" + // 用户ID条件
                        "AND (" +
                        // 运营人员权限：产品+渠道匹配
                        "(urb.resource_type = 'PRODUCT_CHANNEL' " +
                        "AND %s.productId = urb.product_id " +
                        "AND %s.channelCode = urb.channel_code) " +

                        // 运维人员权限：产品+渠道+区服匹配
                        "OR (urb.resource_type = 'SERVER' " +
                        "AND %s.productId = urb.product_id " +
                        "AND %s.channelCode = urb.channel_code " +
                        "  ) " +
                        ")" +
                        ")",
                userIdsCondition,
                tableAlias, tableAlias,  // PRODUCT_CHANNEL 条件
                tableAlias, tableAlias, tableAlias  // SERVER 条件
        );
    }
}
