<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mandong.api.module.erp.dal.mysql.order.SdkOrderCallgameMapper">
  <resultMap id="BaseResultMap" type="com.mandong.api.module.erp.dal.dataobject.order.SdkOrderCallgame">
    <!--@mbg.generated-->
    <!--@Table qsdk_order_callgame-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="orderNo" jdbcType="VARCHAR" property="orderno" />
    <result column="createTime" jdbcType="INTEGER" property="createtime" />
    <result column="callParams" jdbcType="LONGVARCHAR" property="callparams" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="callUrl" jdbcType="VARCHAR" property="callurl" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, orderNo, createTime, callParams, `status`, callUrl
  </sql>
</mapper>