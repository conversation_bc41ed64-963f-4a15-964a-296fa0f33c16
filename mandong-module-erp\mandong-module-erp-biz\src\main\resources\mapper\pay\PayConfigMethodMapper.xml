<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mandong.api.module.erp.dal.mysql.pay.PayConfigMethodMapper">
  <resultMap id="BaseResultMap" type="com.mandong.api.module.erp.dal.dataobject.pay.PayConfigMethodDO">
    <!--@mbg.generated-->
    <!--@Table pay_config_method-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="method_id" jdbcType="BIGINT" property="methodId" />
    <result column="status" jdbcType="BOOLEAN" property="status" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, config_id, method_id, `status`, sort, creator, create_time, updater, update_time, 
    deleted, tenant_id
  </sql>
</mapper>