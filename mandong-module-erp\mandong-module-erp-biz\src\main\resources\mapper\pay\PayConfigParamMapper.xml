<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mandong.api.module.erp.dal.mysql.pay.PayConfigParamMapper">
  <resultMap id="BaseResultMap" type="com.mandong.api.module.erp.dal.dataobject.pay.PayConfigParamDO">
    <!--@mbg.generated-->
    <!--@Table pay_config_param-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="param_key" jdbcType="VARCHAR" property="paramKey" />
    <result column="param_value" jdbcType="LONGVARCHAR" property="paramValue" />
    <result column="param_type" jdbcType="VARCHAR" property="paramType" />
    <result column="is_encrypted" jdbcType="BOOLEAN" property="isEncrypted" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, config_id, param_key, param_value, param_type, is_encrypted, description, creator, 
    create_time, updater, update_time, deleted, tenant_id
  </sql>
</mapper>