<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mandong.api.module.erp.dal.mysql.pay.PayMethodMapper">
  <resultMap id="BaseResultMap" type="com.mandong.api.module.erp.dal.dataobject.pay.PayMethodDO">
    <!--@mbg.generated-->
    <!--@Table pay_method-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="provider" jdbcType="VARCHAR" property="provider" />
    <result column="icon_url" jdbcType="VARCHAR" property="iconUrl" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="BOOLEAN" property="status" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, code, `name`, provider, icon_url, description, sort, `status`, creator, create_time, 
    updater, update_time, deleted, tenant_id
  </sql>
</mapper>