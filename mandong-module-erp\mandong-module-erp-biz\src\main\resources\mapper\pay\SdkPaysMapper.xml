<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mandong.api.module.erp.dal.mysql.pay.SdkPaysMapper">
  <resultMap id="BaseResultMap" type="com.mandong.api.module.erp.dal.dataobject.pay.SdkPays">
    <!--@mbg.generated-->
    <!--@Table qsdk_pays-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="payName" jdbcType="VARCHAR" property="payname" />
    <result column="payDesc" jdbcType="LONGVARCHAR" property="paydesc" />
    <result column="payLogo" jdbcType="VARCHAR" property="paylogo" />
    <result column="payModel" jdbcType="LONGVARCHAR" property="paymodel" />
    <result column="status" jdbcType="BOOLEAN" property="status" />
    <result column="lang" jdbcType="VARCHAR" property="lang" />
    <result column="supportWap" jdbcType="BOOLEAN" property="supportwap" />
    <result column="parentId" jdbcType="INTEGER" property="parentid" />
    <result column="isFixed" jdbcType="BOOLEAN" property="isfixed" />
    <result column="typeCode" jdbcType="VARCHAR" property="typecode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, payName, payDesc, payLogo, payModel, `status`, lang, supportWap, parentId, isFixed, 
    typeCode
  </sql>
</mapper>