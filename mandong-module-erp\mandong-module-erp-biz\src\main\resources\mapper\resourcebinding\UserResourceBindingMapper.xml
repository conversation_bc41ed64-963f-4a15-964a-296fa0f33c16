<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mandong.api.module.erp.dal.mysql.resourcebinding.UserResourceBindingMapper">

    <!-- 通用结果映射 -->
    <resultMap id="BaseResultMap" type="com.mandong.api.module.erp.dal.dataobject.resourcebinding.UserResourceBindingDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="ops_dept_id" property="opsDeptId" />
        <result column="resource_type" property="resourceType" />
        <result column="target_dept_id" property="targetDeptId" />
        <result column="product_id" property="productId" />
        <result column="channel_code" property="channelCode" />
        <result column="operation_team" property="operationTeam" />
        <result column="server_name" property="serverName" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 根据用户ID查询资源绑定 -->
    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT * FROM qsdk_user_resource_binding
        WHERE user_id = #{userId} AND deleted = 0 AND status = 1
        ORDER BY create_time DESC
    </select>

    <!-- 根据用户ID和资源类型查询绑定 -->
    <select id="selectByUserIdAndResourceType" resultMap="BaseResultMap">
        SELECT * FROM qsdk_user_resource_binding
        WHERE user_id = #{userId} AND resource_type = #{resourceType} 
              AND deleted = 0 AND status = 1
        ORDER BY create_time DESC
    </select>

    <!-- 根据部门ID查询资源绑定 -->
    <select id="selectByDeptId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT * FROM qsdk_user_resource_binding
        WHERE (ops_dept_id = #{deptId} OR target_dept_id = #{deptId}) 
              AND deleted = 0 AND status = 1
        ORDER BY create_time DESC
    </select>

    <!-- 批量删除用户资源绑定 -->
    <update id="deleteByUserId" parameterType="java.lang.Long">
        UPDATE qsdk_user_resource_binding 
        SET deleted = 1, update_time = NOW()
        WHERE user_id = #{userId} AND deleted = 0
    </update>

    <!-- 获取用户可访问的产品ID列表 -->
    <select id="selectAccessibleProductIds" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT DISTINCT product_id FROM qsdk_user_resource_binding
        WHERE user_id = #{userId} AND resource_type = 'PRODUCT_CHANNEL'
              AND product_id IS NOT NULL AND deleted = 0 AND status = 1
    </select>

    <!-- 获取用户可访问的渠道代码列表 -->
    <select id="selectAccessibleChannelCodes" parameterType="java.lang.Long" resultType="java.lang.String">
        SELECT DISTINCT channel_code FROM qsdk_user_resource_binding
        WHERE user_id = #{userId} AND resource_type = 'PRODUCT_CHANNEL'
              AND channel_code IS NOT NULL AND channel_code != '' 
              AND deleted = 0 AND status = 1
    </select>

    <!-- 获取用户可访问的运营队伍列表 -->
    <select id="selectAccessibleOperationTeams" parameterType="java.lang.Long" resultType="java.lang.String">
        SELECT DISTINCT operation_team FROM qsdk_user_resource_binding
        WHERE user_id = #{userId} AND resource_type = 'OPERATION_TEAM'
              AND operation_team IS NOT NULL AND operation_team != '' 
              AND deleted = 0 AND status = 1
    </select>

    <!-- 获取用户可访问的区服列表 -->
    <select id="selectAccessibleServerNames" parameterType="java.lang.Long" resultType="java.lang.String">
        SELECT DISTINCT server_name FROM qsdk_user_resource_binding
        WHERE user_id = #{userId} AND resource_type = 'SERVER'
              AND server_name IS NOT NULL AND server_name != '' 
              AND deleted = 0 AND status = 1
    </select>

    <!-- 获取用户的完整权限绑定组合（用于生成精确权限SQL） -->
    <select id="selectUserPermissionCombinations" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT * FROM qsdk_user_resource_binding
        WHERE user_id = #{userId} AND deleted = 0 AND status = 1
        ORDER BY resource_type, product_id, channel_code, server_name
    </select>

</mapper> 