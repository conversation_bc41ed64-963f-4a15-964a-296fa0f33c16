<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.mandong.api</groupId>
  <artifactId>mandong-module-infra-biz</artifactId>
  <version>2.4.1-SNAPSHOT</version>
  <name>mandong-module-infra-biz</name>
  <description>infra 模块，主要提供两块能力：
            1. 我们放基础设施的运维与管理，支撑上层的通用与核心业务。 例如说：定时任务的管理、服务器的信息等等
            2. 研发工具，提升研发效率与质量。 例如说：代码生成器、接口文档等等</description>
  <url>https://github.com/YunaiV/ruoyi-vue-pro/mandong-module-infra/mandong-module-infra-biz</url>
  <dependencies>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-module-system-api</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-module-infra-api</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-biz-tenant</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-security</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-websocket</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-mybatis</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-generator</artifactId>
      <version>3.5.9</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-redis</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-job</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-mq</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-excel</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.velocity</groupId>
      <artifactId>velocity-engine-core</artifactId>
      <version>2.4.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-monitor</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>de.codecentric</groupId>
      <artifactId>spring-boot-admin-starter-server</artifactId>
      <version>3.4.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>de.codecentric</groupId>
          <artifactId>spring-boot-admin-server-cloud</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>commons-net</groupId>
      <artifactId>commons-net</artifactId>
      <version>3.11.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.jcraft</groupId>
      <artifactId>jsch</artifactId>
      <version>0.1.55</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-s3</artifactId>
      <version>1.12.777</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.tika</groupId>
      <artifactId>tika-core</artifactId>
      <version>2.9.2</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <id>huaweicloud</id>
      <name>huawei</name>
      <url>https://mirrors.huaweicloud.com/repository/maven/</url>
    </repository>
    <repository>
      <id>aliyunmaven</id>
      <name>aliyun</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </repository>
    <repository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>spring-milestones</id>
      <name>Spring Milestones</name>
      <url>https://repo.spring.io/milestone</url>
    </repository>
    <repository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <id>spring-snapshots</id>
      <name>Spring Snapshots</name>
      <url>https://repo.spring.io/snapshot</url>
    </repository>
  </repositories>
</project>
