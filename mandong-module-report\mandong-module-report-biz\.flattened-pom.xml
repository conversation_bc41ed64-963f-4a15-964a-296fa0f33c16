<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.mandong.api</groupId>
  <artifactId>mandong-module-report-biz</artifactId>
  <version>2.4.1-SNAPSHOT</version>
  <name>mandong-module-report-biz</name>
  <description>report 模块，主要实现数据可视化报表等功能：
        1. 基于「积木报表」实现，打印设计、报表设计、图形设计、大屏设计等。</description>
  <url>https://github.com/YunaiV/ruoyi-vue-pro/mandong-module-report/mandong-module-report-biz</url>
  <dependencies>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-module-report-api</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-module-system-api</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-biz-tenant</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-web</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-security</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-mybatis</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jeecgframework.jimureport</groupId>
      <artifactId>jimureport-spring-boot3-starter-fastjson2</artifactId>
      <version>1.8.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.alibaba</groupId>
          <artifactId>druid</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-excel</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <id>huaweicloud</id>
      <name>huawei</name>
      <url>https://mirrors.huaweicloud.com/repository/maven/</url>
    </repository>
    <repository>
      <id>aliyunmaven</id>
      <name>aliyun</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </repository>
    <repository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>spring-milestones</id>
      <name>Spring Milestones</name>
      <url>https://repo.spring.io/milestone</url>
    </repository>
    <repository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <id>spring-snapshots</id>
      <name>Spring Snapshots</name>
      <url>https://repo.spring.io/snapshot</url>
    </repository>
  </repositories>
</project>
