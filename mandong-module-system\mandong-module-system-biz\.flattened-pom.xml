<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.mandong.api</groupId>
  <artifactId>mandong-module-system-biz</artifactId>
  <version>2.4.1-SNAPSHOT</version>
  <name>mandong-module-system-biz</name>
  <description>system 模块下，我们放通用业务，支撑上层的核心业务。
        例如说：用户、部门、权限、数据字典等等</description>
  <url>https://github.com/YunaiV/ruoyi-vue-pro/mandong-module-system/mandong-module-system-biz</url>
  <dependencies>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-module-system-api</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-module-infra-api</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-biz-data-permission</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-biz-tenant</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-biz-ip</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-security</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
      <version>3.4.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-mybatis</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-redis</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-job</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-mq</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mandong.api</groupId>
      <artifactId>mandong-spring-boot-starter-excel</artifactId>
      <version>2.4.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-mail</artifactId>
      <version>3.4.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.xingyuv</groupId>
      <artifactId>spring-boot-starter-justauth</artifactId>
      <version>2.0.5</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>cn.hutool</groupId>
          <artifactId>hutool-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.github.binarywang</groupId>
      <artifactId>wx-java-mp-spring-boot-starter</artifactId>
      <version>4.6.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.github.binarywang</groupId>
      <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>
      <version>4.6.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.xingyuv</groupId>
      <artifactId>spring-boot-starter-captcha-plus</artifactId>
      <version>2.0.3</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.dromara.hutool</groupId>
      <artifactId>hutool-extra</artifactId>
      <version>6.0.0-M19</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <id>huaweicloud</id>
      <name>huawei</name>
      <url>https://mirrors.huaweicloud.com/repository/maven/</url>
    </repository>
    <repository>
      <id>aliyunmaven</id>
      <name>aliyun</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </repository>
    <repository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>spring-milestones</id>
      <name>Spring Milestones</name>
      <url>https://repo.spring.io/milestone</url>
    </repository>
    <repository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <id>spring-snapshots</id>
      <name>Spring Snapshots</name>
      <url>https://repo.spring.io/snapshot</url>
    </repository>
  </repositories>
</project>
