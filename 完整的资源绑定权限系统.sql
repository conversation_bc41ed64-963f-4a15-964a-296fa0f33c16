-- =========================================
-- 基于资源绑定的权限控制系统
-- =========================================

-- 1. 用户资源绑定表
CREATE TABLE `qsdk_user_resource_binding` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `ops_dept_id` bigint NOT NULL COMMENT '运维部门ID',
  `resource_type` varchar(50) NOT NULL COMMENT '绑定资源类型：OPERATION_TEAM（运营小组）、PRODUCT_CHANNEL（产品渠道）、SERVER（区服）',
  `target_dept_id` bigint DEFAULT NULL COMMENT '目标运营部门ID',
  `product_id` bigint DEFAULT NULL COMMENT '绑定的产品ID（如果是产品相关）',
  `channel_code` varchar(100) DEFAULT NULL COMMENT '绑定的渠道代码（如果是渠道相关）',
  `server_name` varchar(100) DEFAULT NULL COMMENT '绑定的区服名称（如果是区服相关）',
  `operation_team` varchar(100) DEFAULT NULL COMMENT '绑定的运营小组名称',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：1启用，0停用',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `deleted` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除：1已删除，0未删除',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_ops_dept_id` (`ops_dept_id`),
  KEY `idx_resource_type` (`resource_type`),
  KEY `idx_target_dept_id` (`target_dept_id`),
  -- 权限查询性能优化索引
  KEY `idx_user_permission_check` (`user_id`, `resource_type`, `deleted`, `status`),
  KEY `idx_product_channel` (`product_id`, `channel_code`),
  KEY `idx_server_binding` (`product_id`, `channel_code`, `server_name`),
  KEY `idx_operation_team` (`operation_team`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户资源绑定表';

-- =========================================
-- 性能优化索引说明
-- =========================================

/*
索引优化说明：

1. idx_user_permission_check: 
   - 用于优化权限子查询的主要索引
   - 覆盖 user_id, resource_type, deleted, status 字段
   - 支持快速过滤用户的有效权限绑定

2. idx_product_channel:
   - 优化产品+渠道组合查询
   - 支持 PRODUCT_CHANNEL 类型的权限匹配

3. idx_server_binding:
   - 优化产品+渠道+区服组合查询  
   - 支持 SERVER 类型的权限匹配

4. idx_operation_team:
   - 优化运营队伍权限查询
   - 支持 OPERATION_TEAM 类型的权限匹配

查询性能对比：
- 优化前：可能需要扫描整个权限绑定表
- 优化后：通过索引快速定位，查询时间从秒级降到毫秒级
*/

-- =========================================
-- 示例数据插入
-- =========================================

-- 运维1组组员（用户ID=201）绑定运营1部1小组A+区服
INSERT INTO `qsdk_user_resource_binding` 
(`user_id`, `ops_dept_id`, `resource_type`, `target_dept_id`, `product_id`, `channel_code`, `server_name`, `operation_team`, `status`, `remark`) 
VALUES
(201, 11, 'OPERATION_TEAM', 21, 1001, 'channel_a', 'server_1', '运营1部1小组A', 1, '运维1组组员绑定运营1部1小组A+区服'),
(201, 11, 'OPERATION_TEAM', 22, 1002, 'channel_b', 'server_2', '运营2部1小组', 1, '运维1组组员绑定运营2部1小组+区服');

-- 运维2组组员（用户ID=202）绑定运营3部2小组+区服
INSERT INTO `qsdk_user_resource_binding` 
(`user_id`, `ops_dept_id`, `resource_type`, `target_dept_id`, `product_id`, `channel_code`, `server_name`, `operation_team`, `status`, `remark`) 
VALUES
(202, 12, 'OPERATION_TEAM', 23, 1003, 'channel_c', 'server_3', '运营3部2小组', 1, '运维2组组员绑定运营3部2小组+区服');

-- 运营组员绑定游戏+渠道示例
INSERT INTO `qsdk_user_resource_binding` 
(`user_id`, `ops_dept_id`, `resource_type`, `target_dept_id`, `product_id`, `channel_code`, `operation_team`, `status`, `remark`) 
VALUES
(301, 21, 'PRODUCT_CHANNEL', 21, 2001, 'game_channel_a', '运营1组', 1, '运营1组组员绑定游戏1+渠道A'),
(301, 21, 'PRODUCT_CHANNEL', 21, 2002, 'game_channel_b', '运营1组', 1, '运营1组组员绑定游戏2+渠道B'),
(302, 22, 'PRODUCT_CHANNEL', 22, 2003, 'game_channel_c', '运营2组', 1, '运营2组组员绑定游戏3+渠道C');

-- =========================================
-- 权限查询验证
-- =========================================

/*
权限逻辑验证场景：

1. 运维主管（假设用户ID=100）：
   - 可以查看运维1组和运维2组所有组员绑定的数据
   - 包括：运营1部1小组A+区服、运营2部1小组+区服、运营3部2小组+区服

2. 运维1组组长（假设用户ID=150）：
   - 可以查看运维1组组员（用户ID=201）绑定的数据
   - 包括：运营1部1小组A+区服、运营2部1小组+区服

3. 运维1组组员（用户ID=201）：
   - 只能查看自己绑定的数据
   - 包括：运营1部1小组A+区服、运营2部1小组+区服

4. 运维2组组长（假设用户ID=160）：
   - 可以查看运维2组组员（用户ID=202）绑定的数据
   - 包括：运营3部2小组+区服

5. 运维2组组员（用户ID=202）：
   - 只能查看自己绑定的数据
   - 包括：运营3部2小组+区服
*/

-- 查询示例SQL：

-- 查询用户201可访问的产品ID
SELECT DISTINCT product_id FROM qsdk_user_resource_binding 
WHERE user_id = 201 AND status = 1 AND deleted = 0;

-- 查询用户201可访问的渠道
SELECT DISTINCT channel_code FROM qsdk_user_resource_binding 
WHERE user_id = 201 AND status = 1 AND deleted = 0;

-- 查询用户201可访问的区服
SELECT DISTINCT server_name FROM qsdk_user_resource_binding 
WHERE user_id = 201 AND status = 1 AND deleted = 0;

-- 查询用户201绑定的所有资源详情
SELECT * FROM qsdk_user_resource_binding 
WHERE user_id = 201 AND status = 1 AND deleted = 0 
ORDER BY create_time DESC;

-- =========================================
-- 权限控制逻辑说明
-- =========================================

/*
系统特点：

1. 灵活的资源绑定：
   - 运维人员可以绑定多个运营部门的资源
   - 支持产品、渠道、区服、运营小组等多维度绑定

2. 层级权限控制：
   - 主管：查看所有下属绑定的资源
   - 组长：查看本组组员绑定的资源
   - 组员：只能查看自己绑定的资源

3. 数据隔离：
   - 运维1组和运维2组之间数据完全隔离
   - 不同主管之间数据完全隔离

4. 动态权限：
   - 通过修改绑定关系动态调整权限
   - 支持启用/停用特定绑定

5. 审计追踪：
   - 完整的创建、更新时间记录
   - 支持软删除和状态管理
*/ 