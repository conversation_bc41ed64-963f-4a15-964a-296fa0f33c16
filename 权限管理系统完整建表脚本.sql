-- SDK运维管理系统 - 分层权限管理完整建表脚本
-- 支持删除重建，确保所有字段完整

-- 删除旧表（按依赖关系倒序删除）
DROP TABLE IF EXISTS qsdk_user_hierarchy_cache;
DROP TABLE IF EXISTS qsdk_user_permission_mapping;
DROP TABLE IF EXISTS qsdk_permission_resource;
DROP TABLE IF EXISTS qsdk_user_dept_relation;
DROP TABLE IF EXISTS qsdk_department;

-- 1. 部门组织表
CREATE TABLE qsdk_department (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '部门ID',
    parent_id BIGINT NOT NULL DEFAULT 0 COMMENT '上级部门ID，0表示根部门',
    dept_name VARCHAR(50) NOT NULL COMMENT '部门名称',
    dept_code VARCHAR(20) NOT NULL COMMENT '部门编码',
    dept_level TINYINT NOT NULL DEFAULT 1 COMMENT '部门层级：1-主管部门，2-组长部门，3-组员部门',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '显示排序',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-停用，1-启用',
    description VARCHAR(200) COMMENT '部门描述',
    creator VARCHAR(50) COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(50) COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    
    UNIQUE KEY uk_dept_code (dept_code, deleted),
    INDEX idx_parent_id (parent_id),
    INDEX idx_dept_level (dept_level),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门组织表';

-- 2. 用户部门关系表
CREATE TABLE qsdk_user_dept_relation (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关系ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    dept_id BIGINT NOT NULL COMMENT '部门ID',
    relation_type TINYINT NOT NULL DEFAULT 1 COMMENT '关系类型：1-正式，2-临时',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-停用，1-启用',
    creator VARCHAR(50) COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(50) COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    
    UNIQUE KEY uk_user_dept (user_id, dept_id, deleted),
    INDEX idx_user_id (user_id),
    INDEX idx_dept_id (dept_id),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time),
    
    FOREIGN KEY (dept_id) REFERENCES qsdk_department(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户部门关系表';

-- 3. 权限资源表
CREATE TABLE qsdk_permission_resource (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '资源ID',
    resource_type VARCHAR(20) NOT NULL COMMENT '资源类型：PRODUCT-产品，CHANNEL-渠道，SERVER-区服',
    resource_id VARCHAR(50) NOT NULL COMMENT '资源标识ID',
    resource_name VARCHAR(100) NOT NULL COMMENT '资源名称',
    description VARCHAR(200) COMMENT '资源描述',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-停用，1-启用',
    creator VARCHAR(50) COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(50) COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    
    UNIQUE KEY uk_resource (resource_type, resource_id, deleted),
    INDEX idx_resource_type (resource_type),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限资源表';

-- 4. 用户权限映射表
CREATE TABLE qsdk_user_permission_mapping (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '权限映射ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    resource_type VARCHAR(20) NOT NULL COMMENT '资源类型：PRODUCT-产品，CHANNEL-渠道，SERVER-区服',
    resource_id VARCHAR(50) NOT NULL COMMENT '资源标识ID',
    permission_type VARCHAR(20) NOT NULL DEFAULT 'READ' COMMENT '权限类型：READ-只读，READ_WRITE-读写，ALL-全部权限',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-停用，1-启用',
    creator VARCHAR(50) COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(50) COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    
    UNIQUE KEY uk_user_resource (user_id, resource_type, resource_id, deleted),
    INDEX idx_user_id (user_id),
    INDEX idx_resource_type (resource_type),
    INDEX idx_resource_id (resource_id),
    INDEX idx_permission_type (permission_type),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户权限映射表';

-- 5. 用户层级缓存表（用于性能优化）
CREATE TABLE qsdk_user_hierarchy_cache (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '缓存ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_type VARCHAR(20) NOT NULL COMMENT '角色类型：SUPERVISOR-主管，TEAM_LEADER-组长，TEAM_MEMBER-组员',
    department_ids TEXT COMMENT '所属部门ID列表（JSON格式）',
    subordinate_ids TEXT COMMENT '下属用户ID列表（JSON格式）',
    accessible_product_ids TEXT COMMENT '可访问产品ID列表（JSON格式）',
    accessible_channels TEXT COMMENT '可访问渠道列表（JSON格式）',
    accessible_servers TEXT COMMENT '可访问区服列表（JSON格式）',
    cache_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '缓存时间',
    expire_time DATETIME NOT NULL COMMENT '过期时间',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-失效，1-有效',
    creator VARCHAR(50) COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(50) COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    
    UNIQUE KEY uk_user_id (user_id, deleted),
    INDEX idx_role_type (role_type),
    INDEX idx_cache_time (cache_time),
    INDEX idx_expire_time (expire_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户层级缓存表';

-- 插入初始数据

-- 1. 创建两个独立的主管部门树结构
INSERT INTO qsdk_department (parent_id, dept_name, dept_code, dept_level, sort_order, status, description, creator, create_time) VALUES
-- 主管A的部门树
(0, 'SDK运维A部', 'SDK_A_HQ', 1, 1, 1, '主管A管理的SDK运维部门', 'admin', NOW()),
(1, 'A部-游戏1运维组', 'A_GAME1_OPS', 2, 1, 1, 'A部负责游戏1的运维工作', 'admin', NOW()),
(1, 'A部-游戏2运维组', 'A_GAME2_OPS', 2, 2, 1, 'A部负责游戏2的运维工作', 'admin', NOW()),
(2, 'A部-游戏1渠道1组', 'A_G1_CH1', 3, 1, 1, 'A部游戏1渠道1组员', 'admin', NOW()),
(2, 'A部-游戏1渠道2组', 'A_G1_CH2', 3, 2, 1, 'A部游戏1渠道2组员', 'admin', NOW()),
(3, 'A部-游戏2渠道1组', 'A_G2_CH1', 3, 1, 1, 'A部游戏2渠道1组员', 'admin', NOW()),

-- 主管B的部门树（独立）
(0, 'SDK运维B部', 'SDK_B_HQ', 1, 10, 1, '主管B管理的SDK运维部门', 'admin', NOW()),
(7, 'B部-游戏3运维组', 'B_GAME3_OPS', 2, 1, 1, 'B部负责游戏3的运维工作', 'admin', NOW()),
(7, 'B部-游戏4运维组', 'B_GAME4_OPS', 2, 2, 1, 'B部负责游戏4的运维工作', 'admin', NOW()),
(8, 'B部-游戏3渠道1组', 'B_G3_CH1', 3, 1, 1, 'B部游戏3渠道1组员', 'admin', NOW()),
(9, 'B部-游戏4渠道1组', 'B_G4_CH1', 3, 1, 1, 'B部游戏4渠道1组员', 'admin', NOW());

-- 2. 分配用户到部门（包含必要的 status 字段）
-- 主管A (用户ID=1) - 属于A部
INSERT INTO qsdk_user_dept_relation (user_id, dept_id, relation_type, status, creator, create_time) VALUES
(1, 1, 1, 1, 'admin', NOW());

-- 主管B (用户ID=2) - 属于B部  
INSERT INTO qsdk_user_dept_relation (user_id, dept_id, relation_type, status, creator, create_time) VALUES
(2, 7, 1, 1, 'admin', NOW());

-- A部的组长和组员
INSERT INTO qsdk_user_dept_relation (user_id, dept_id, relation_type, status, creator, create_time) VALUES
-- A部组长
(3, 2, 1, 1, 'admin', NOW()), -- 游戏1组长
(4, 3, 1, 1, 'admin', NOW()), -- 游戏2组长
-- A部组员
(5, 4, 1, 1, 'admin', NOW()), -- 游戏1渠道1组员
(6, 5, 1, 1, 'admin', NOW()), -- 游戏1渠道2组员
(7, 6, 1, 1, 'admin', NOW()); -- 游戏2渠道1组员

-- B部的组长和组员
INSERT INTO qsdk_user_dept_relation (user_id, dept_id, relation_type, status, creator, create_time) VALUES
-- B部组长
(8, 8, 1, 1, 'admin', NOW()), -- 游戏3组长
(9, 9, 1, 1, 'admin', NOW()), -- 游戏4组长
-- B部组员
(10, 10, 1, 1, 'admin', NOW()), -- 游戏3渠道1组员
(11, 11, 1, 1, 'admin', NOW()); -- 游戏4渠道1组员

-- 3. 创建权限资源（产品、渠道、区服）
INSERT INTO qsdk_permission_resource (resource_type, resource_id, resource_name, description, status, creator, create_time) VALUES
-- A部管理的产品
('PRODUCT', '1', '游戏1', 'A部管理的游戏1', 1, 'admin', NOW()),
('PRODUCT', '2', '游戏2', 'A部管理的游戏2', 1, 'admin', NOW()),
-- B部管理的产品
('PRODUCT', '3', '游戏3', 'B部管理的游戏3', 1, 'admin', NOW()),
('PRODUCT', '4', '游戏4', 'B部管理的游戏4', 1, 'admin', NOW()),

-- A部管理的渠道
('CHANNEL', 'A_CH01', 'A部渠道1', 'A部管理的渠道1', 1, 'admin', NOW()),
('CHANNEL', 'A_CH02', 'A部渠道2', 'A部管理的渠道2', 1, 'admin', NOW()),
('CHANNEL', 'A_CH03', 'A部渠道3', 'A部管理的渠道3', 1, 'admin', NOW()),
-- B部管理的渠道
('CHANNEL', 'B_CH01', 'B部渠道1', 'B部管理的渠道1', 1, 'admin', NOW()),
('CHANNEL', 'B_CH02', 'B部渠道2', 'B部管理的渠道2', 1, 'admin', NOW()),

-- A部管理的区服
('SERVER', 'A_S1', 'A部游戏1区服1', 'A部游戏1区服1', 1, 'admin', NOW()),
('SERVER', 'A_S2', 'A部游戏1区服2', 'A部游戏1区服2', 1, 'admin', NOW()),
('SERVER', 'A_S3', 'A部游戏2区服1', 'A部游戏2区服1', 1, 'admin', NOW()),
-- B部管理的区服
('SERVER', 'B_S1', 'B部游戏3区服1', 'B部游戏3区服1', 1, 'admin', NOW()),
('SERVER', 'B_S2', 'B部游戏4区服1', 'B部游戏4区服1', 1, 'admin', NOW());

-- 4. 分配权限 - 主管A（用户ID=1）只有A部相关的权限
INSERT INTO qsdk_user_permission_mapping (user_id, resource_type, resource_id, permission_type, status, creator, create_time) VALUES
-- 主管A的产品权限
(1, 'PRODUCT', '1', 'ALL', 1, 'admin', NOW()),
(1, 'PRODUCT', '2', 'ALL', 1, 'admin', NOW()),
-- 主管A的渠道权限
(1, 'CHANNEL', 'A_CH01', 'ALL', 1, 'admin', NOW()),
(1, 'CHANNEL', 'A_CH02', 'ALL', 1, 'admin', NOW()),
(1, 'CHANNEL', 'A_CH03', 'ALL', 1, 'admin', NOW()),
-- 主管A的区服权限
(1, 'SERVER', 'A_S1', 'ALL', 1, 'admin', NOW()),
(1, 'SERVER', 'A_S2', 'ALL', 1, 'admin', NOW()),
(1, 'SERVER', 'A_S3', 'ALL', 1, 'admin', NOW());

-- 5. 分配权限 - 主管B（用户ID=2）只有B部相关的权限
INSERT INTO qsdk_user_permission_mapping (user_id, resource_type, resource_id, permission_type, status, creator, create_time) VALUES
-- 主管B的产品权限
(2, 'PRODUCT', '3', 'ALL', 1, 'admin', NOW()),
(2, 'PRODUCT', '4', 'ALL', 1, 'admin', NOW()),
-- 主管B的渠道权限
(2, 'CHANNEL', 'B_CH01', 'ALL', 1, 'admin', NOW()),
(2, 'CHANNEL', 'B_CH02', 'ALL', 1, 'admin', NOW()),
-- 主管B的区服权限
(2, 'SERVER', 'B_S1', 'ALL', 1, 'admin', NOW()),
(2, 'SERVER', 'B_S2', 'ALL', 1, 'admin', NOW());

-- 6. A部组长和组员的权限分配
-- A部游戏1组长（用户ID=3）
INSERT INTO qsdk_user_permission_mapping (user_id, resource_type, resource_id, permission_type, status, creator, create_time) VALUES
(3, 'PRODUCT', '1', 'READ_WRITE', 1, 'admin', NOW()),
(3, 'CHANNEL', 'A_CH01', 'READ_WRITE', 1, 'admin', NOW()),
(3, 'CHANNEL', 'A_CH02', 'READ_WRITE', 1, 'admin', NOW()),
(3, 'SERVER', 'A_S1', 'READ_WRITE', 1, 'admin', NOW()),
(3, 'SERVER', 'A_S2', 'READ_WRITE', 1, 'admin', NOW());

-- A部游戏2组长（用户ID=4）
INSERT INTO qsdk_user_permission_mapping (user_id, resource_type, resource_id, permission_type, status, creator, create_time) VALUES
(4, 'PRODUCT', '2', 'READ_WRITE', 1, 'admin', NOW()),
(4, 'CHANNEL', 'A_CH03', 'READ_WRITE', 1, 'admin', NOW()),
(4, 'SERVER', 'A_S3', 'READ_WRITE', 1, 'admin', NOW());

-- A部组员权限（用户ID=5,6,7）
INSERT INTO qsdk_user_permission_mapping (user_id, resource_type, resource_id, permission_type, status, creator, create_time) VALUES
-- 游戏1渠道1组员（用户ID=5）
(5, 'PRODUCT', '1', 'READ', 1, 'admin', NOW()),
(5, 'CHANNEL', 'A_CH01', 'READ', 1, 'admin', NOW()),
(5, 'SERVER', 'A_S1', 'READ', 1, 'admin', NOW()),
-- 游戏1渠道2组员（用户ID=6）
(6, 'PRODUCT', '1', 'READ', 1, 'admin', NOW()),
(6, 'CHANNEL', 'A_CH02', 'READ', 1, 'admin', NOW()),
(6, 'SERVER', 'A_S2', 'READ', 1, 'admin', NOW()),
-- 游戏2渠道1组员（用户ID=7）
(7, 'PRODUCT', '2', 'READ', 1, 'admin', NOW()),
(7, 'CHANNEL', 'A_CH03', 'READ', 1, 'admin', NOW()),
(7, 'SERVER', 'A_S3', 'READ', 1, 'admin', NOW());

-- 7. B部组长和组员的权限分配
-- B部游戏3组长（用户ID=8）
INSERT INTO qsdk_user_permission_mapping (user_id, resource_type, resource_id, permission_type, status, creator, create_time) VALUES
(8, 'PRODUCT', '3', 'READ_WRITE', 1, 'admin', NOW()),
(8, 'CHANNEL', 'B_CH01', 'READ_WRITE', 1, 'admin', NOW()),
(8, 'SERVER', 'B_S1', 'READ_WRITE', 1, 'admin', NOW());

-- B部游戏4组长（用户ID=9）
INSERT INTO qsdk_user_permission_mapping (user_id, resource_type, resource_id, permission_type, status, creator, create_time) VALUES
(9, 'PRODUCT', '4', 'READ_WRITE', 1, 'admin', NOW()),
(9, 'CHANNEL', 'B_CH02', 'READ_WRITE', 1, 'admin', NOW()),
(9, 'SERVER', 'B_S2', 'READ_WRITE', 1, 'admin', NOW());

-- B部组员权限（用户ID=10,11）
INSERT INTO qsdk_user_permission_mapping (user_id, resource_type, resource_id, permission_type, status, creator, create_time) VALUES
-- 游戏3渠道1组员（用户ID=10）
(10, 'PRODUCT', '3', 'READ', 1, 'admin', NOW()),
(10, 'CHANNEL', 'B_CH01', 'READ', 1, 'admin', NOW()),
(10, 'SERVER', 'B_S1', 'READ', 1, 'admin', NOW()),
-- 游戏4渠道1组员（用户ID=11）
(11, 'PRODUCT', '4', 'READ', 1, 'admin', NOW()),
(11, 'CHANNEL', 'B_CH02', 'READ', 1, 'admin', NOW()),
(11, 'SERVER', 'B_S2', 'READ', 1, 'admin', NOW());

-- 验证脚本 - 检查数据隔离效果
-- 查看部门结构
SELECT 
    '=== 部门结构 ===' as info,
    d.id,
    d.dept_name,
    d.dept_level,
    CASE d.dept_level 
        WHEN 1 THEN '🔴主管部门'
        WHEN 2 THEN '🟡组长部门' 
        WHEN 3 THEN '🟢组员部门'
        ELSE '❓未知'
    END as dept_type,
    d.parent_id,
    (SELECT parent.dept_name FROM qsdk_department parent WHERE parent.id = d.parent_id) as parent_name
FROM qsdk_department d 
WHERE d.deleted = 0
ORDER BY d.id;

-- 查看主管A能看到的用户范围（应该只有用户3,4,5,6,7）
SELECT 
    '=== 主管A权限范围 ===' as info,
    u.user_id,
    d.dept_name,
    CASE d.dept_level 
        WHEN 1 THEN '主管'
        WHEN 2 THEN '组长'
        WHEN 3 THEN '组员'
    END as role_type
FROM qsdk_user_dept_relation u
JOIN qsdk_department d ON u.dept_id = d.id
WHERE u.deleted = 0 AND u.status = 1 AND d.deleted = 0
AND d.id IN (
    SELECT id FROM qsdk_department WHERE id = 1 AND deleted = 0
    UNION ALL
    SELECT id FROM qsdk_department WHERE parent_id = 1 AND deleted = 0
    UNION ALL  
    SELECT id FROM qsdk_department WHERE parent_id IN (SELECT id FROM qsdk_department WHERE parent_id = 1 AND deleted = 0) AND deleted = 0
) AND u.user_id != 1
ORDER BY u.user_id;

-- 查看主管B能看到的用户范围（应该只有用户8,9,10,11）
SELECT 
    '=== 主管B权限范围 ===' as info,
    u.user_id,
    d.dept_name,
    CASE d.dept_level 
        WHEN 1 THEN '主管'
        WHEN 2 THEN '组长'
        WHEN 3 THEN '组员'
    END as role_type
FROM qsdk_user_dept_relation u
JOIN qsdk_department d ON u.dept_id = d.id
WHERE u.deleted = 0 AND u.status = 1 AND d.deleted = 0
AND d.id IN (
    SELECT id FROM qsdk_department WHERE id = 7 AND deleted = 0
    UNION ALL
    SELECT id FROM qsdk_department WHERE parent_id = 7 AND deleted = 0
    UNION ALL  
    SELECT id FROM qsdk_department WHERE parent_id IN (SELECT id FROM qsdk_department WHERE parent_id = 7 AND deleted = 0) AND deleted = 0
) AND u.user_id != 2
ORDER BY u.user_id;

-- 查看权限资源分配隔离
SELECT 
    '=== 权限隔离验证 ===' as info,
    u.user_id,
    CASE u.user_id 
        WHEN 1 THEN '主管A'
        WHEN 2 THEN '主管B'
        ELSE CONCAT('用户', u.user_id)
    END as user_name,
    pm.resource_type,
    pm.resource_id,
    pr.resource_name,
    pm.permission_type
FROM qsdk_user_permission_mapping pm
JOIN qsdk_permission_resource pr ON pm.resource_type = pr.resource_type AND pm.resource_id = pr.resource_id AND pr.deleted = 0
JOIN qsdk_user_dept_relation u ON pm.user_id = u.user_id AND u.deleted = 0
WHERE pm.deleted = 0 AND pm.status = 1 AND pm.user_id IN (1, 2) -- 只看两个主管的权限
ORDER BY pm.user_id, pm.resource_type, pm.resource_id;

-- 验证完整性
SELECT 
    '=== 数据统计 ===' as info,
    (SELECT COUNT(*) FROM qsdk_department WHERE deleted = 0) as dept_count,
    (SELECT COUNT(*) FROM qsdk_user_dept_relation WHERE deleted = 0) as user_dept_count,
    (SELECT COUNT(*) FROM qsdk_permission_resource WHERE deleted = 0) as resource_count,
    (SELECT COUNT(*) FROM qsdk_user_permission_mapping WHERE deleted = 0) as permission_count; 