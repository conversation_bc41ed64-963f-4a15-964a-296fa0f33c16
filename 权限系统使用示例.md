# 🛡️ 权限系统使用示例

## 📋 **系统架构说明**

### 核心组件
1. **部门管理** - 三层级部门结构（主管/组长/组员）
2. **资源绑定** - 基于用户-资源的精确权限控制
3. **权限注解** - 方法级别的自动权限检查
4. **数据拦截** - AOP自动拦截权限验证

### 部门类型识别
- **运营部门** (`OPERATION`) - 管理游戏产品和渠道
- **运维部门** (`OPS`) - 管理运营队伍和区服

---

## 🔧 **后端权限注解使用**

### 1. **产品权限检查**

```java
@RestController
public class OrderController {
    
    // 检查用户是否有任何产品权限
    @RequireProductPermission(checkAllProducts = true)
    @GetMapping("/orders")
    public CommonResult<PageResult<OrderVO>> getOrders() {
        // 用户必须至少有一个产品的访问权限才能访问
        return success(orderService.getOrders());
    }
    
    // 检查用户对特定产品的权限
    @RequireProductPermission(productIdParamIndex = 0)
    @GetMapping("/orders/by-product")
    public CommonResult<List<OrderVO>> getOrdersByProduct(@RequestParam Long productId) {
        // 用户必须有该产品的访问权限
        return success(orderService.getOrdersByProduct(productId));
    }
}
```

### 2. **渠道权限检查**

```java
@RestController
public class ChannelController {
    
    @RequireChannelPermission(channelCodeParamIndex = 0)
    @GetMapping("/channel-data")
    public CommonResult<ChannelDataVO> getChannelData(@RequestParam String channelCode) {
        return success(channelService.getChannelData(channelCode));
    }
    
    @RequireChannelPermission(checkAllChannels = true)
    @GetMapping("/all-channels")
    public CommonResult<List<ChannelVO>> getAllChannels() {
        return success(channelService.getAllChannels());
    }
}
```

### 3. **运营队伍权限检查**

```java
@RestController
public class OperationController {
    
    @RequireOperationTeamPermission(operationTeamParamIndex = 0)
    @GetMapping("/team-stats")
    public CommonResult<TeamStatsVO> getTeamStats(@RequestParam String operationTeam) {
        return success(operationService.getTeamStats(operationTeam));
    }
}
```

### 4. **区服权限检查**

```java
@RestController
public class ServerController {
    
    @RequireServerPermission(serverNameParamIndex = 0)
    @GetMapping("/server-status")
    public CommonResult<ServerStatusVO> getServerStatus(@RequestParam String serverName) {
        return success(serverService.getServerStatus(serverName));
    }
}
```

---

## 🎯 **前端权限配置**

### 1. **部门用户管理界面**

```typescript
// UserManageDialog.vue 关键配置
const isOperationDept = () => {
  // 优先使用deptType字段判断
  if (selectedDept.value?.deptType) {
    return selectedDept.value.deptType === 'OPERATION'
  }
  // 降级方案：基于部门名称判断
  return selectedDept.value?.deptName?.includes('运营')
}

const isOpsDept = () => {
  if (selectedDept.value?.deptType) {
    return selectedDept.value.deptType === 'OPS'
  }
  return selectedDept.value?.deptName?.includes('运维')
}
```

### 2. **资源绑定配置示例**

#### 运营部门绑定 - 产品渠道
```javascript
// 运营部门：配置游戏产品和渠道
const operationBinding = {
  userId: 1001,
  productId: 1,        // 游戏产品ID
  channelCode: 'ANDROID', // 渠道代码
  status: 1,
  remark: '负责安卓渠道运营'
}
```

#### 运维部门绑定 - 运营队伍和区服
```javascript
// 运维部门：配置运营队伍和区服
const opsBinding = {
  userId: 2001,
  targetDeptId: 1,        // 目标运营部门ID
  operationTeam: 'A组',   // 运营队伍
  serverName: '正式服1',   // 区服名称
  status: 1,
  remark: '负责A组和正式服1的运维'
}
```

---

## 🔄 **API调用示例**

### 1. **用户资源绑定API**

```javascript
// 获取用户资源绑定
const bindings = await UserResourceBindingApi.getUserResourceBinding(userId)

// 批量设置用户资源绑定
await UserResourceBindingApi.batchSetUserResourceBinding({
  userId: 1001,
  bindings: [
    {
      productId: 1,
      channelCode: 'ANDROID',
      status: 1,
      remark: '安卓渠道绑定'
    },
    {
      productId: 1,
      channelCode: 'IOS',
      status: 1,
      remark: 'iOS渠道绑定'
    }
  ]
})

// 测试用户权限
const hasPermission = await UserResourceBindingApi.testUserPermission(
  userId, 
  'PRODUCT_CHANNEL', 
  '1'  // 产品ID
)
```

### 2. **部门管理API**

```javascript
// 分配用户到部门
await DepartmentApi.assignUserToDepartment({
  userId: 1001,
  deptIds: [1, 2]  // 可以分配到多个部门
})

// 获取部门用户列表
const users = await DepartmentApi.getDepartmentUsers(deptId)

// 移除用户部门关系
await DepartmentApi.removeUserFromDepartment(userId, deptId)
```

---

## 🗄️ **数据库操作示例**

### 1. **部门类型设置**

```sql
-- 执行部门类型字段添加脚本
source 部门类型字段添加脚本.sql

-- 手动设置特定部门类型
UPDATE qsdk_department 
SET dept_type = 'OPERATION' 
WHERE id IN (1,2,3);  -- 运营部门IDs

UPDATE qsdk_department 
SET dept_type = 'OPS' 
WHERE id IN (4,5,6);  -- 运维部门IDs
```

### 2. **资源绑定数据**

```sql
-- 执行资源绑定表建表脚本
source 资源绑定表建表脚本.sql

-- 查看用户权限
SELECT 
    u.user_id,
    u.resource_type,
    CASE u.resource_type
        WHEN 'PRODUCT_CHANNEL' THEN CONCAT('产品:', u.product_id, ' 渠道:', u.channel_code)
        WHEN 'OPERATION_TEAM' THEN CONCAT('运营队伍:', u.operation_team)
        WHEN 'SERVER' THEN CONCAT('区服:', u.server_name)
    END as resource_info
FROM qsdk_user_resource_binding u
WHERE u.user_id = 1001 AND u.deleted = 0 AND u.status = 1;
```

---

## 🎮 **业务场景示例**

### 场景1：运营人员查看数据
```java
@GetMapping("/game-revenue")
@RequireProductPermission(productIdParamIndex = 0)
public CommonResult<RevenueVO> getGameRevenue(@RequestParam Long productId) {
    // 只有绑定了该产品权限的运营人员才能查看
    return success(revenueService.getRevenue(productId));
}
```

### 场景2：运维人员管理区服
```java
@PostMapping("/restart-server")
@RequireServerPermission(serverNameParamIndex = 0)
public CommonResult<Boolean> restartServer(@RequestParam String serverName) {
    // 只有绑定了该区服权限的运维人员才能重启
    serverService.restart(serverName);
    return success(true);
}
```

### 场景3：跨部门数据查询
```java
@GetMapping("/cross-dept-stats")
@RequireOperationTeamPermission(checkAllTeams = true)
public CommonResult<StatsVO> getCrossDeptStats() {
    Long userId = getCurrentUserId();
    List<String> accessibleTeams = resourceBindingService.getUserBoundOperationTeams(userId);
    // 基于用户权限动态构建查询条件
    return success(statsService.getStatsByTeams(accessibleTeams));
}
```

---

## 🚀 **部署和配置**

### 1. **数据库初始化**
```bash
# 1. 执行部门类型字段添加
mysql -u root -p < 部门类型字段添加脚本.sql

# 2. 执行资源绑定表建表
mysql -u root -p < 资源绑定表建表脚本.sql

# 3. 根据实际情况调整部门类型
```

### 2. **权限验证测试**
```bash
# 测试权限API
curl -X GET "http://localhost:8080/erp/resource-binding/test-permission?userId=1001&resourceType=PRODUCT_CHANNEL&resourceValue=1"

# 查看用户绑定资源
curl -X GET "http://localhost:8080/erp/resource-binding/user-bindings?userId=1001"
```

---

## 📈 **权限效果验证**

### 成功案例
- ✅ 运营A组用户只能查看绑定产品的数据
- ✅ 运维用户只能管理授权的区服
- ✅ 部门转移时权限自动更新
- ✅ 权限异常时提供明确错误信息

### 安全保障
- 🛡️ 方法级权限自动拦截
- 🔐 数据库层面权限控制
- 📊 完整的权限审计日志
- ⚡ 高性能权限查询缓存

通过这套完整的权限系统，实现了细粒度的资源访问控制，保证了数据安全和业务隔离。 