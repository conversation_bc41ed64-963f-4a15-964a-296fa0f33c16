-- SDK运维管理系统 - 主管数据隔离测试脚本
-- 演示：用户1(主管A)只能看到自己下面的数据，用户2(主管B)只能看到自己下面的数据

-- 清理现有测试数据
DELETE FROM qsdk_user_hierarchy_cache;
DELETE FROM qsdk_user_permission_mapping;
DELETE FROM qsdk_permission_resource;
DELETE FROM qsdk_user_dept_relation;
DELETE FROM qsdk_department;

-- 1. 创建两个独立的主管部门树结构
INSERT INTO qsdk_department (parent_id, dept_name, dept_code, dept_level, sort_order, status, description, creator, create_time) VALUES
-- 主管A的部门树
(0, 'SDK运维A部', 'SDK_A_HQ', 1, 1, 1, '主管A管理的SDK运维部门', 'admin', NOW()),
(1, 'A部-游戏1运维组', 'A_GAME1_OPS', 2, 1, 1, 'A部负责游戏1的运维工作', 'admin', NOW()),
(1, 'A部-游戏2运维组', 'A_GAME2_OPS', 2, 2, 1, 'A部负责游戏2的运维工作', 'admin', NOW()),
(2, 'A部-游戏1渠道1组', 'A_G1_CH1', 3, 1, 1, 'A部游戏1渠道1组员', 'admin', NOW()),
(2, 'A部-游戏1渠道2组', 'A_G1_CH2', 3, 2, 1, 'A部游戏1渠道2组员', 'admin', NOW()),
(3, 'A部-游戏2渠道1组', 'A_G2_CH1', 3, 1, 1, 'A部游戏2渠道1组员', 'admin', NOW()),

-- 主管B的部门树（独立）
(0, 'SDK运维B部', 'SDK_B_HQ', 1, 10, 1, '主管B管理的SDK运维部门', 'admin', NOW()),
(7, 'B部-游戏3运维组', 'B_GAME3_OPS', 2, 1, 1, 'B部负责游戏3的运维工作', 'admin', NOW()),
(7, 'B部-游戏4运维组', 'B_GAME4_OPS', 2, 2, 1, 'B部负责游戏4的运维工作', 'admin', NOW()),
(8, 'B部-游戏3渠道1组', 'B_G3_CH1', 3, 1, 1, 'B部游戏3渠道1组员', 'admin', NOW()),
(9, 'B部-游戏4渠道1组', 'B_G4_CH1', 3, 1, 1, 'B部游戏4渠道1组员', 'admin', NOW());

-- 2. 分配用户到部门
-- 主管A (用户ID=1) - 属于A部
INSERT INTO qsdk_user_dept_relation (user_id, dept_id, relation_type, status, creator, create_time) VALUES
(1, 1, 1, 1, 'admin', NOW());

-- 主管B (用户ID=2) - 属于B部  
INSERT INTO qsdk_user_dept_relation (user_id, dept_id, relation_type, status, creator, create_time) VALUES
(2, 7, 1, 1, 'admin', NOW());

-- A部的组长和组员
INSERT INTO qsdk_user_dept_relation (user_id, dept_id, relation_type, status, creator, create_time) VALUES
-- A部组长
(3, 2, 1, 1, 'admin', NOW()), -- 游戏1组长
(4, 3, 1, 1, 'admin', NOW()), -- 游戏2组长
-- A部组员
(5, 4, 1, 1, 'admin', NOW()), -- 游戏1渠道1组员
(6, 5, 1, 1, 'admin', NOW()), -- 游戏1渠道2组员
(7, 6, 1, 1, 'admin', NOW()); -- 游戏2渠道1组员

-- B部的组长和组员
INSERT INTO qsdk_user_dept_relation (user_id, dept_id, relation_type, status, creator, create_time) VALUES
-- B部组长
(8, 8, 1, 1, 'admin', NOW()), -- 游戏3组长
(9, 9, 1, 1, 'admin', NOW()), -- 游戏4组长
-- B部组员
(10, 10, 1, 1, 'admin', NOW()), -- 游戏3渠道1组员
(11, 11, 1, 1, 'admin', NOW()); -- 游戏4渠道1组员

-- 3. 创建权限资源（产品、渠道、区服）
INSERT INTO qsdk_permission_resource (resource_type, resource_id, resource_name, description, status, creator, create_time) VALUES
-- A部管理的产品
('PRODUCT', '1', '游戏1', 'A部管理的游戏1', 1, 'admin', NOW()),
('PRODUCT', '2', '游戏2', 'A部管理的游戏2', 1, 'admin', NOW()),
-- B部管理的产品
('PRODUCT', '3', '游戏3', 'B部管理的游戏3', 1, 'admin', NOW()),
('PRODUCT', '4', '游戏4', 'B部管理的游戏4', 1, 'admin', NOW()),

-- A部管理的渠道
('CHANNEL', 'A_CH01', 'A部渠道1', 'A部管理的渠道1', 1, 'admin', NOW()),
('CHANNEL', 'A_CH02', 'A部渠道2', 'A部管理的渠道2', 1, 'admin', NOW()),
('CHANNEL', 'A_CH03', 'A部渠道3', 'A部管理的渠道3', 1, 'admin', NOW()),
-- B部管理的渠道
('CHANNEL', 'B_CH01', 'B部渠道1', 'B部管理的渠道1', 1, 'admin', NOW()),
('CHANNEL', 'B_CH02', 'B部渠道2', 'B部管理的渠道2', 1, 'admin', NOW()),

-- A部管理的区服
('SERVER', 'A_S1', 'A部游戏1区服1', 'A部游戏1区服1', 1, 'admin', NOW()),
('SERVER', 'A_S2', 'A部游戏1区服2', 'A部游戏1区服2', 1, 'admin', NOW()),
('SERVER', 'A_S3', 'A部游戏2区服1', 'A部游戏2区服1', 1, 'admin', NOW()),
-- B部管理的区服
('SERVER', 'B_S1', 'B部游戏3区服1', 'B部游戏3区服1', 1, 'admin', NOW()),
('SERVER', 'B_S2', 'B部游戏4区服1', 'B部游戏4区服1', 1, 'admin', NOW());

-- 4. 分配权限 - 主管A（用户ID=1）只有A部相关的权限
INSERT INTO qsdk_user_permission_mapping (user_id, resource_type, resource_id, permission_type, status, creator, create_time) VALUES
-- 主管A的产品权限
(1, 'PRODUCT', '1', 'ALL', 1, 'admin', NOW()),
(1, 'PRODUCT', '2', 'ALL', 1, 'admin', NOW()),
-- 主管A的渠道权限
(1, 'CHANNEL', 'A_CH01', 'ALL', 1, 'admin', NOW()),
(1, 'CHANNEL', 'A_CH02', 'ALL', 1, 'admin', NOW()),
(1, 'CHANNEL', 'A_CH03', 'ALL', 1, 'admin', NOW()),
-- 主管A的区服权限
(1, 'SERVER', 'A_S1', 'ALL', 1, 'admin', NOW()),
(1, 'SERVER', 'A_S2', 'ALL', 1, 'admin', NOW()),
(1, 'SERVER', 'A_S3', 'ALL', 1, 'admin', NOW());

-- 5. 分配权限 - 主管B（用户ID=2）只有B部相关的权限
INSERT INTO qsdk_user_permission_mapping (user_id, resource_type, resource_id, permission_type, status, creator, create_time) VALUES
-- 主管B的产品权限
(2, 'PRODUCT', '3', 'ALL', 1, 'admin', NOW()),
(2, 'PRODUCT', '4', 'ALL', 1, 'admin', NOW()),
-- 主管B的渠道权限
(2, 'CHANNEL', 'B_CH01', 'ALL', 1, 'admin', NOW()),
(2, 'CHANNEL', 'B_CH02', 'ALL', 1, 'admin', NOW()),
-- 主管B的区服权限
(2, 'SERVER', 'B_S1', 'ALL', 1, 'admin', NOW()),
(2, 'SERVER', 'B_S2', 'ALL', 1, 'admin', NOW());

-- 6. A部组长和组员的权限分配
-- A部游戏1组长（用户ID=3）
INSERT INTO qsdk_user_permission_mapping (user_id, resource_type, resource_id, permission_type, status, creator, create_time) VALUES
(3, 'PRODUCT', '1', 'READ_WRITE', 1, 'admin', NOW()),
(3, 'CHANNEL', 'A_CH01', 'READ_WRITE', 1, 'admin', NOW()),
(3, 'CHANNEL', 'A_CH02', 'READ_WRITE', 1, 'admin', NOW()),
(3, 'SERVER', 'A_S1', 'READ_WRITE', 1, 'admin', NOW()),
(3, 'SERVER', 'A_S2', 'READ_WRITE', 1, 'admin', NOW());

-- A部游戏2组长（用户ID=4）
INSERT INTO qsdk_user_permission_mapping (user_id, resource_type, resource_id, permission_type, status, creator, create_time) VALUES
(4, 'PRODUCT', '2', 'READ_WRITE', 1, 'admin', NOW()),
(4, 'CHANNEL', 'A_CH03', 'READ_WRITE', 1, 'admin', NOW()),
(4, 'SERVER', 'A_S3', 'READ_WRITE', 1, 'admin', NOW());

-- A部组员权限（用户ID=5,6,7）
INSERT INTO qsdk_user_permission_mapping (user_id, resource_type, resource_id, permission_type, status, creator, create_time) VALUES
-- 游戏1渠道1组员（用户ID=5）
(5, 'PRODUCT', '1', 'READ', 1, 'admin', NOW()),
(5, 'CHANNEL', 'A_CH01', 'READ', 1, 'admin', NOW()),
(5, 'SERVER', 'A_S1', 'READ', 1, 'admin', NOW()),
-- 游戏1渠道2组员（用户ID=6）
(6, 'PRODUCT', '1', 'READ', 1, 'admin', NOW()),
(6, 'CHANNEL', 'A_CH02', 'READ', 1, 'admin', NOW()),
(6, 'SERVER', 'A_S2', 'READ', 1, 'admin', NOW()),
-- 游戏2渠道1组员（用户ID=7）
(7, 'PRODUCT', '2', 'READ', 1, 'admin', NOW()),
(7, 'CHANNEL', 'A_CH03', 'READ', 1, 'admin', NOW()),
(7, 'SERVER', 'A_S3', 'READ', 1, 'admin', NOW());

-- 7. B部组长和组员的权限分配
-- B部游戏3组长（用户ID=8）
INSERT INTO qsdk_user_permission_mapping (user_id, resource_type, resource_id, permission_type, status, creator, create_time) VALUES
(8, 'PRODUCT', '3', 'READ_WRITE', 1, 'admin', NOW()),
(8, 'CHANNEL', 'B_CH01', 'READ_WRITE', 1, 'admin', NOW()),
(8, 'SERVER', 'B_S1', 'READ_WRITE', 1, 'admin', NOW());

-- B部游戏4组长（用户ID=9）
INSERT INTO qsdk_user_permission_mapping (user_id, resource_type, resource_id, permission_type, status, creator, create_time) VALUES
(9, 'PRODUCT', '4', 'READ_WRITE', 1, 'admin', NOW()),
(9, 'CHANNEL', 'B_CH02', 'READ_WRITE', 1, 'admin', NOW()),
(9, 'SERVER', 'B_S2', 'READ_WRITE', 1, 'admin', NOW());

-- B部组员权限（用户ID=10,11）
INSERT INTO qsdk_user_permission_mapping (user_id, resource_type, resource_id, permission_type, status, creator, create_time) VALUES
-- 游戏3渠道1组员（用户ID=10）
(10, 'PRODUCT', '3', 'READ', 1, 'admin', NOW()),
(10, 'CHANNEL', 'B_CH01', 'READ', 1, 'admin', NOW()),
(10, 'SERVER', 'B_S1', 'READ', 1, 'admin', NOW()),
-- 游戏4渠道1组员（用户ID=11）
(11, 'PRODUCT', '4', 'READ', 1, 'admin', NOW()),
(11, 'CHANNEL', 'B_CH02', 'READ', 1, 'admin', NOW()),
(11, 'SERVER', 'B_S2', 'READ', 1, 'admin', NOW());

-- 8. 验证数据隔离效果的查询
-- 查看部门结构
SELECT 
    d.id,
    d.dept_name,
    d.dept_level,
    CASE d.dept_level 
        WHEN 1 THEN '🔴主管部门'
        WHEN 2 THEN '🟡组长部门' 
        WHEN 3 THEN '🟢组员部门'
        ELSE '❓未知'
    END as dept_type,
    d.parent_id,
    (SELECT parent.dept_name FROM qsdk_department parent WHERE parent.id = d.parent_id) as parent_name
FROM qsdk_department d 
ORDER BY d.id;

-- 查看主管A能看到的用户范围（应该只有用户3,4,5,6,7）
SELECT 
    '主管A权限范围' as scope_type,
    u.user_id,
    d.dept_name,
    CASE d.dept_level 
        WHEN 1 THEN '主管'
        WHEN 2 THEN '组长'
        WHEN 3 THEN '组员'
    END as role_type
FROM qsdk_user_dept_relation u
JOIN qsdk_department d ON u.dept_id = d.id
WHERE d.id IN (
    SELECT id FROM qsdk_department WHERE id = 1 
    UNION ALL
    SELECT id FROM qsdk_department WHERE parent_id = 1
    UNION ALL  
    SELECT id FROM qsdk_department WHERE parent_id IN (SELECT id FROM qsdk_department WHERE parent_id = 1)
) AND u.user_id != 1
ORDER BY u.user_id;

-- 查看主管B能看到的用户范围（应该只有用户8,9,10,11）
SELECT 
    '主管B权限范围' as scope_type,
    u.user_id,
    d.dept_name,
    CASE d.dept_level 
        WHEN 1 THEN '主管'
        WHEN 2 THEN '组长'
        WHEN 3 THEN '组员'
    END as role_type
FROM qsdk_user_dept_relation u
JOIN qsdk_department d ON u.dept_id = d.id
WHERE d.id IN (
    SELECT id FROM qsdk_department WHERE id = 7
    UNION ALL
    SELECT id FROM qsdk_department WHERE parent_id = 7
    UNION ALL  
    SELECT id FROM qsdk_department WHERE parent_id IN (SELECT id FROM qsdk_department WHERE parent_id = 7)
) AND u.user_id != 2
ORDER BY u.user_id;

-- 查看权限资源分配隔离
SELECT 
    '权限隔离验证' as test_type,
    u.user_id,
    CASE u.user_id 
        WHEN 1 THEN '主管A'
        WHEN 2 THEN '主管B'
        ELSE CONCAT('用户', u.user_id)
    END as user_name,
    pm.resource_type,
    pm.resource_id,
    pr.resource_name
FROM qsdk_user_permission_mapping pm
JOIN qsdk_permission_resource pr ON pm.resource_type = pr.resource_type AND pm.resource_id = pr.resource_id
JOIN qsdk_user_dept_relation u ON pm.user_id = u.user_id
WHERE pm.user_id IN (1, 2) -- 只看两个主管的权限
ORDER BY pm.user_id, pm.resource_type, pm.resource_id; 