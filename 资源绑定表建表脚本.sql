-- ===============================================
-- 用户资源绑定表建表脚本
-- ===============================================

-- 创建用户资源绑定表
CREATE TABLE IF NOT EXISTS qsdk_user_resource_binding (
    id BIGINT AUTO_INCREMENT COMMENT '绑定ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    ops_dept_id BIGINT COMMENT '运维部门ID',
    resource_type VARCHAR(50) NOT NULL COMMENT '资源类型：PRODUCT_CHANNEL-产品渠道，OPERATION_TEAM-运营队伍，SERVER-区服',
    target_dept_id BIGINT COMMENT '目标部门ID（运营部门ID）',
    product_id BIGINT COMMENT '产品ID',
    channel_code VARCHAR(50) COMMENT '渠道代码',
    operation_team VARCHAR(100) COMMENT '运营队伍名称',
    server_name VARCHAR(100) COMMENT '区服名称',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    remark VARCHAR(500) COMMENT '备注',
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：1-删除，0-未删除',
    PRIMARY KEY (id),
    INDEX idx_user_id (user_id),
    INDEX idx_ops_dept_id (ops_dept_id),
    INDEX idx_resource_type (resource_type),
    INDEX idx_target_dept_id (target_dept_id),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户资源绑定表';

-- ===============================================
-- 示例数据插入（可选）
-- ===============================================

-- 运营部门用户的产品渠道绑定示例
INSERT INTO qsdk_user_resource_binding (user_id, ops_dept_id, resource_type, product_id, channel_code, status, remark) VALUES
(1001, 1, 'PRODUCT_CHANNEL', 1, 'ANDROID', 1, '安卓渠道绑定'),
(1001, 1, 'PRODUCT_CHANNEL', 1, 'IOS', 1, 'iOS渠道绑定'),
(1002, 1, 'PRODUCT_CHANNEL', 2, 'ANDROID', 1, '游戏2安卓渠道绑定');

-- 运维部门用户的运营队伍绑定示例  
INSERT INTO qsdk_user_resource_binding (user_id, ops_dept_id, resource_type, target_dept_id, operation_team, status, remark) VALUES
(2001, 2, 'OPERATION_TEAM', 1, 'A组', 1, '负责A组运营'),
(2002, 2, 'OPERATION_TEAM', 1, 'B组', 1, '负责B组运营');

-- 运维部门用户的区服绑定示例
INSERT INTO qsdk_user_resource_binding (user_id, ops_dept_id, resource_type, server_name, status, remark) VALUES
(2001, 2, 'SERVER', '测试服1', 1, '测试服管理'),
(2001, 2, 'SERVER', '正式服1', 1, '正式服管理'),
(2002, 2, 'SERVER', '测试服2', 1, '测试服2管理');

-- ===============================================
-- 查询验证脚本
-- ===============================================

-- 检查表结构
DESCRIBE qsdk_user_resource_binding;

-- 检查数据
SELECT 
    id,
    user_id,
    resource_type,
    CASE resource_type
        WHEN 'PRODUCT_CHANNEL' THEN CONCAT('产品:', product_id, ' 渠道:', channel_code)
        WHEN 'OPERATION_TEAM' THEN CONCAT('运营队伍:', operation_team)
        WHEN 'SERVER' THEN CONCAT('区服:', server_name)
        ELSE '未知'
    END as resource_info,
    status,
    remark,
    create_time
FROM qsdk_user_resource_binding 
WHERE deleted = 0
ORDER BY user_id, resource_type, id;

-- 按用户统计绑定资源数量
SELECT 
    user_id,
    resource_type,
    COUNT(*) as binding_count,
    GROUP_CONCAT(
        CASE resource_type
            WHEN 'PRODUCT_CHANNEL' THEN CONCAT(product_id, ':', channel_code)
            WHEN 'OPERATION_TEAM' THEN operation_team
            WHEN 'SERVER' THEN server_name
        END
    ) as resource_list
FROM qsdk_user_resource_binding 
WHERE deleted = 0 AND status = 1
GROUP BY user_id, resource_type
ORDER BY user_id, resource_type; 