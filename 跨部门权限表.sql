-- 跨部门权限表
CREATE TABLE `qsdk_cross_dept_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `source_user_id` bigint NOT NULL COMMENT '源用户ID（运维人员）',
  `source_dept_id` bigint NOT NULL COMMENT '源部门ID（运维部门）',
  `target_dept_id` bigint NOT NULL COMMENT '目标部门ID（运营部门）',
  `permission_type` varchar(50) NOT NULL COMMENT '权限类型：LEAD_GROUP_MANAGE（带队管理）、OPT_GROUP_MANAGE（运维组管理）',
  `permission_scope` varchar(20) NOT NULL DEFAULT 'ALL' COMMENT '权限范围：ALL（全部）、SPECIFIC（指定范围）',
  `resource_ids` text COMMENT '具体权限资源ID（如果是SPECIFIC范围）',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：1启用，0停用',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `deleted` tinyint NOT NULL DEFAULT 0 COMMENT '逻辑删除：1已删除，0未删除',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_source_user_id` (`source_user_id`),
  KEY `idx_target_dept_id` (`target_dept_id`),
  KEY `idx_permission_type` (`permission_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='跨部门权限表';

-- 插入示例数据
INSERT INTO `qsdk_cross_dept_permission` (`source_user_id`, `source_dept_id`, `target_dept_id`, `permission_type`, `permission_scope`, `status`, `remark`) VALUES
(101, 1, 21, 'LEAD_GROUP_MANAGE', 'ALL', 1, '运维人员张三可以管理运营A部所有带队组'),
(102, 1, 22, 'LEAD_GROUP_MANAGE', 'ALL', 1, '运维人员李四可以管理运营B部所有带队组'),
(103, 1, 21, 'OPT_GROUP_MANAGE', 'ALL', 1, '运维人员王五可以管理运营A部所有运维组');

-- 解决方案说明
/*
这个跨部门权限表的设计思路：

1. 权限类型分为：
   - LEAD_GROUP_MANAGE：带队管理权限
   - OPT_GROUP_MANAGE：运维组管理权限

2. 权限范围：
   - ALL：对目标部门的所有相关资源有管理权限
   - SPECIFIC：只对指定的资源有管理权限

3. 使用场景：
   - 运维部门的人员需要管理运营部门的带队组
   - 运维部门的人员需要管理运营部门的运维组
   - 支持细粒度的跨部门权限控制

4. 集成方式：
   - 在DataPermissionService中添加跨部门权限检查
   - 在SdkLeadGroupService中集成跨部门权限验证
   - 在SdkOptService中集成跨部门权限验证
*/ 