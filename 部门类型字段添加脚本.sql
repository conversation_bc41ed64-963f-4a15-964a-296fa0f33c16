-- ===============================================
-- 部门类型识别优化脚本
-- ===============================================

-- 1. 添加部门类型字段
ALTER TABLE qsdk_department 
ADD COLUMN dept_type VARCHAR(20) COMMENT '部门业务类型：OPERATION-运营部门，OPS-运维部门' 
AFTER description;

-- 2. 根据部门名称自动识别并更新现有数据
UPDATE qsdk_department 
SET dept_type = CASE 
    WHEN dept_name LIKE '%运营%' THEN 'OPERATION'
    WHEN dept_name LIKE '%运维%' THEN 'OPS'
    ELSE NULL
END 
WHERE deleted = 0;

-- 3. 为未能自动识别的部门手动设置类型（可根据实际情况调整）
-- 示例：根据部门编码规则设置
UPDATE qsdk_department 
SET dept_type = CASE 
    WHEN dept_code LIKE 'OP_%' THEN 'OPERATION'
    WHEN dept_code LIKE 'OPS_%' THEN 'OPS'
    WHEN dept_code LIKE 'SDK_%' THEN 'OPS'  -- SDK开头的默认为运维
    ELSE dept_type
END 
WHERE deleted = 0 AND dept_type IS NULL;

-- 4. 查看更新结果
SELECT 
    id,
    dept_name,
    dept_code,
    dept_level,
    dept_type,
    CASE dept_type 
        WHEN 'OPERATION' THEN '🔵运营部门'
        WHEN 'OPS' THEN '🟢运维部门'
        ELSE '❓未分类'
    END as dept_type_desc
FROM qsdk_department 
WHERE deleted = 0
ORDER BY dept_type, dept_level, id;

-- 5. 检查需要手动分类的部门
SELECT 
    '需要手动分类的部门' as notice,
    id,
    dept_name,
    dept_code,
    dept_level
FROM qsdk_department 
WHERE deleted = 0 AND dept_type IS NULL;

-- ===============================================
-- 索引优化（可选）
-- ===============================================
ALTER TABLE qsdk_department ADD INDEX idx_dept_type (dept_type);

-- ===============================================
-- 示例：手动更新特定部门类型
-- ===============================================
-- UPDATE qsdk_department SET dept_type = 'OPERATION' WHERE id IN (1,2,3);
-- UPDATE qsdk_department SET dept_type = 'OPS' WHERE id IN (4,5,6); 